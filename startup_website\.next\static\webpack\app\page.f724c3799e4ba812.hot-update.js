"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx":
/*!**************************************************!*\
  !*** ./src/components/WhyAutomation/WhyAuto.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* harmony import */ var _Scroll__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Scroll */ \"(app-pages-browser)/./src/components/WhyAutomation/Scroll.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst WhyAuto = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 pt-24 md:pt-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"Why Automation?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4 md:pl-0 md:pr-0\",\n                                    children: \"Transform Your Business With Intelligent Automation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/[0.7] text-lg md:text-xl pl-5 pr-5 leading-relaxed md:pl-0 md:pr-0\",\n                                    children: \"Deploy AI-powered automation to eliminate inefficiencies, reduce costs, and create significant competitive advantages in your industry.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.3,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-[95%] md:w-[90%] mx-auto mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Scroll__WEBPACK_IMPORTED_MODULE_3__.WhyAutomationDemo, {}, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = WhyAuto;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhyAuto);\nvar _c;\n$RefreshReg$(_c, \"WhyAuto\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx\n"));

/***/ })

});