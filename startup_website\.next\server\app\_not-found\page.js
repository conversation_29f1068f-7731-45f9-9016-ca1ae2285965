/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2FProduct_Sans_Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-product-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22productSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2FProduct_Sans_Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-product-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22productSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer/Footer.tsx */ \"(rsc)/./src/components/Footer/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar/OuterNav.tsx */ \"(rsc)/./src/components/Navbar/OuterNav.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2FProduct_Sans_Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-product-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22productSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"10903923b197\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcU3RhcnR1cFxcV2Vic2l0ZVxcc3RhcnR1cF93ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMDkwMzkyM2IxOTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_Product_Sans_Regular_ttf_weight_400_style_normal_variable_font_product_sans_variableName_productSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./Product_Sans_Regular.ttf\",\"weight\":\"400\",\"style\":\"normal\"}],\"variable\":\"--font-product-sans\"}],\"variableName\":\"productSans\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":[{\\\"path\\\":\\\"./Product_Sans_Regular.ttf\\\",\\\"weight\\\":\\\"400\\\",\\\"style\\\":\\\"normal\\\"}],\\\"variable\\\":\\\"--font-product-sans\\\"}],\\\"variableName\\\":\\\"productSans\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_Product_Sans_Regular_ttf_weight_400_style_normal_variable_font_product_sans_variableName_productSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_Product_Sans_Regular_ttf_weight_400_style_normal_variable_font_product_sans_variableName_productSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navbar_OuterNav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar/OuterNav */ \"(rsc)/./src/components/Navbar/OuterNav.tsx\");\n/* harmony import */ var _components_Footer_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer/Footer */ \"(rsc)/./src/components/Footer/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_Product_Sans_Regular_ttf_weight_400_style_normal_variable_font_product_sans_variableName_productSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased bg-black text-white`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar_OuterNav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Footer/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Startup\\Website\\startup_website\\src\\components\\Footer\\Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Navbar/OuterNav.tsx":
/*!********************************************!*\
  !*** ./src/components/Navbar/OuterNav.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Startup\\Website\\startup_website\\src\\components\\Navbar\\OuterNav.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2FProduct_Sans_Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-product-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22productSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2FProduct_Sans_Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-product-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22productSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer/Footer.tsx */ \"(ssr)/./src/components/Footer/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar/OuterNav.tsx */ \"(ssr)/./src/components/Navbar/OuterNav.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2FProduct_Sans_Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-product-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22productSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Footer/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubscribe = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setError(\"\");\n        setMessage(\"\");\n        try {\n            const response = await fetch(\"/api/subscribe\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Something went wrong\");\n            }\n            setMessage(data.message);\n            setEmail(\"\");\n        } catch (err) {\n            // Use type assertion with a custom error interface\n            const errorWithMessage = err;\n            setError(errorWithMessage.message);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative overflow-hidden bg-black pb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container relative mx-auto px-6 md:px-8 lg:px-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-16 sm:grid-cols-2 md:grid-cols-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"inline-block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/images/logo.webp\",\n                                        alt: \"Company Logo\",\n                                        width: 140,\n                                        height: 48,\n                                        className: \"h-12 w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-base leading-relaxed text-white/70\",\n                                    children: \"Building intelligent solutions that transform how businesses operate through advanced AI and cutting-edge technology.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 flex space-x-4\",\n                                    children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            \"aria-label\": social.name,\n                                            className: \"flex h-10 w-10 items-center justify-center rounded-full border border-white/10 bg-white/5 text-white/70\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: social.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, social.name, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:grid md:col-span-5 md:grid-cols-3 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-5 text-lg font-semibold text-white\",\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: productLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-sm text-white/60 hover:text-white\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-5 text-lg font-semibold text-white\",\n                                            children: \"Resources\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: resourceLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-sm text-white/60 hover:text-white\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-5 text-lg font-semibold text-white\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: companyLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-sm text-white/60 hover:text-white\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-5 text-lg font-semibold text-white\",\n                                    children: \"Stay Connected\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-5 text-base text-white/70\",\n                                    children: \"Subscribe to receive updates on new features and announcements.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"relative\",\n                                    onSubmit: handleSubscribe,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-hidden rounded-lg bg-white/5 p-0.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    name: \"email\",\n                                                    placeholder: \"Enter your email\",\n                                                    className: \"w-full bg-black/30 py-3 px-4 text-white placeholder:text-white/40 focus:outline-none\",\n                                                    required: true,\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px w-full bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"mt-4 flex w-full items-center justify-center rounded-md bg-white py-3 px-4 text-sm font-medium text-black transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                                    disabled: isSubmitting,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSubmitting ? \"Subscribing...\" : \"Subscribe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        !isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-teal-400\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-red-400\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 pt-8 border-t border-white/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-white/50\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Your Company. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-sm text-white/50 hover:text-white/80\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-sm text-white/50 hover:text-white/80\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-sm text-white/50 hover:text-white/80\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n// Social media icons\nconst socialLinks = [\n    {\n        name: \"Twitter\",\n        href: \"https://twitter.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"LinkedIn\",\n        href: \"https://linkedin.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6.5 21.5h-5v-13h5v13zM4 6.5C2.5 6.5 1.5 5.3 1.5 4s1-2.4 2.5-2.4c1.6 0 2.5 1 2.6 2.5 0 1.4-1 2.5-2.6 2.5zm11.5 6c-1 0-2 1-2 2v7h-5v-13h5v1.8c.8-1 2.1-1.8 3.5-1.8 2.5 0 5 2.1 5 6.1V21.5h-5V14.5c0-1-1-2-2-2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"GitHub\",\n        href: \"https://github.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Instagram\",\n        href: \"https://instagram.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, undefined)\n    }\n];\n// Footer link sections\nconst productLinks = [\n    {\n        name: \"AI Automation\",\n        href: \"/products/ai-automation\"\n    },\n    {\n        name: \"Workflow Tools\",\n        href: \"/products/workflow\"\n    },\n    {\n        name: \"Data Analytics\",\n        href: \"/products/analytics\"\n    },\n    {\n        name: \"Custom Solutions\",\n        href: \"/products/custom\"\n    }\n];\nconst resourceLinks = [\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Documentation\",\n        href: \"/docs\"\n    },\n    {\n        name: \"Case Studies\",\n        href: \"/case-studies\"\n    },\n    {\n        name: \"Help Center\",\n        href: \"/help\"\n    }\n];\nconst companyLinks = [\n    {\n        name: \"About Us\",\n        href: \"/about\"\n    },\n    {\n        name: \"Careers\",\n        href: \"/careers\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    },\n    {\n        name: \"Partners\",\n        href: \"/partners\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar/InnerNav.tsx":
/*!********************************************!*\
  !*** ./src/components/Navbar/InnerNav.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InnerNav: () => (/* binding */ InnerNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/navbar-menu */ \"(ssr)/./src/components/ui/navbar-menu.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ InnerNav auto */ \n\n\n\n\n\nfunction InnerNav({ className }) {\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-x-0 z-[100] w-fit mx-auto py-2\", className),\n        initial: {\n            opacity: 0.9\n        },\n        animate: {\n            opacity: 1\n        },\n        whileHover: {\n            opacity: 1\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.Menu, {\n                setActive: setActive,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/agents\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            setActive: setActive,\n                            active: active,\n                            item: \"AI Agents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/resources\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            setActive: setActive,\n                            active: active,\n                            item: \"Resources\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/about\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            setActive: setActive,\n                            active: active,\n                            item: \"About\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar/InnerNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar/OuterNav.tsx":
/*!********************************************!*\
  !*** ./src/components/Navbar/OuterNav.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _InnerNav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InnerNav */ \"(ssr)/./src/components/Navbar/InnerNav.tsx\");\n/* harmony import */ var _ui_shimmer_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/shimmer-button */ \"(ssr)/./src/components/ui/shimmer-button.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst OuterNav = ()=>{\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Add scroll event listener to change navbar appearance on scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"OuterNav.useEffect\": ()=>{\n            const handleScroll = {\n                \"OuterNav.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 10);\n                }\n            }[\"OuterNav.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"OuterNav.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"OuterNav.useEffect\"];\n        }\n    }[\"OuterNav.useEffect\"], []);\n    // Toggle mobile menu\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 w-full z-50 transition-all duration-300 ${scrolled ? \"backdrop-blur-lg bg-transparent shadow-sm\" : \"backdrop-blur-sm bg-transparent\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-32\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 md:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/images/logo.png\" // Replace with your logo path\n                                ,\n                                alt: \"Company Logo\",\n                                width: 120,\n                                height: 40,\n                                className: \"h-16 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InnerNav__WEBPACK_IMPORTED_MODULE_4__.InnerNav, {\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_shimmer_button__WEBPACK_IMPORTED_MODULE_5__.ShimmerButton, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Book Free Consulting\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-white hover:text-blue-200 focus:outline-none relative w-6 h-6\",\n                                onClick: toggleMobileMenu,\n                                \"aria-label\": \"Toggle menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            className: \"absolute top-2 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu\",\n                                            animate: mobileMenuOpen ? {\n                                                rotate: 45,\n                                                y: 6\n                                            } : {\n                                                rotate: 0,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            className: \"absolute top-3.5 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu\",\n                                            animate: mobileMenuOpen ? {\n                                                opacity: 0\n                                            } : {\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            className: \"absolute top-5 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu\",\n                                            animate: mobileMenuOpen ? {\n                                                rotate: -45,\n                                                y: -6\n                                            } : {\n                                                rotate: 0,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"md:hidden fixed left-0 right-0 overflow-hidden\",\n                        initial: {\n                            height: 0,\n                            opacity: 0\n                        },\n                        animate: {\n                            height: \"auto\",\n                            opacity: 1\n                        },\n                        exit: {\n                            height: 0,\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/90 backdrop-blur-lg shadow-lg py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/agents\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"AI Agents\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/resources\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Resources\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/about\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/contact\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OuterNav);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OYXZiYXIvT3V0ZXJOYXYudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNkI7QUFDRTtBQUNhO0FBQ047QUFDZTtBQUNHO0FBRXhELE1BQU1RLFdBQVc7SUFDZixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1IsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDUyxnQkFBZ0JDLGtCQUFrQixHQUFHViwrQ0FBUUEsQ0FBQztJQUVyRCxrRUFBa0U7SUFDbEVDLGdEQUFTQTs4QkFBQztZQUNSLE1BQU1VO21EQUFlO29CQUNuQkgsWUFBWUksT0FBT0MsT0FBTyxHQUFHO2dCQUMvQjs7WUFFQUQsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7WUFDbEM7c0NBQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7O1FBQ3BEOzZCQUFHLEVBQUU7SUFFTCxxQkFBcUI7SUFDckIsTUFBTUssbUJBQW1CO1FBQ3ZCTixrQkFBa0IsQ0FBQ0Q7SUFDckI7SUFFQSxxQkFDRSw4REFBQ1E7UUFDQ0MsV0FBVyxDQUFDLDJEQUEyRCxFQUNyRVgsV0FDSSw4Q0FDQSxtQ0FDSjtrQkFFRiw0RUFBQ1k7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ3BCLGtEQUFJQTs0QkFBQ3NCLE1BQUs7NEJBQUlGLFdBQVU7c0NBQ3ZCLDRFQUFDbkIsa0RBQUtBO2dDQUNKc0IsS0FBSSxtQkFBbUIsOEJBQThCOztnQ0FDckRDLEtBQUk7Z0NBQ0pDLE9BQU87Z0NBQ1BDLFFBQVE7Z0NBQ1JOLFdBQVU7Ozs7Ozs7Ozs7O3NDQUdkLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ2hCLCtDQUFRQTtnQ0FBQ2dCLFdBQVU7Ozs7Ozs7Ozs7O3NDQUl0Qiw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNwQixrREFBSUE7Z0NBQUNzQixNQUFLOzBDQUNULDRFQUFDakIsNkRBQWFBOzhDQUNaLDRFQUFDc0I7a0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNWiw4REFBQ047NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNRO2dDQUNDUixXQUFVO2dDQUNWUyxTQUFTWDtnQ0FDVFksY0FBVzswQ0FFWCw0RUFBQ1Q7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDZCxpREFBTUEsQ0FBQ3FCLElBQUk7NENBQ1ZQLFdBQVU7NENBQ1ZXLFNBQVNwQixpQkFBaUI7Z0RBQUVxQixRQUFRO2dEQUFJQyxHQUFHOzRDQUFFLElBQUk7Z0RBQUVELFFBQVE7Z0RBQUdDLEdBQUc7NENBQUU7NENBQ25FQyxZQUFZO2dEQUFFQyxVQUFVOzRDQUFJOzs7Ozs7c0RBRTlCLDhEQUFDN0IsaURBQU1BLENBQUNxQixJQUFJOzRDQUNWUCxXQUFVOzRDQUNWVyxTQUFTcEIsaUJBQWlCO2dEQUFFeUIsU0FBUzs0Q0FBRSxJQUFJO2dEQUFFQSxTQUFTOzRDQUFFOzRDQUN4REYsWUFBWTtnREFBRUMsVUFBVTs0Q0FBSTs7Ozs7O3NEQUU5Qiw4REFBQzdCLGlEQUFNQSxDQUFDcUIsSUFBSTs0Q0FDVlAsV0FBVTs0Q0FDVlcsU0FBU3BCLGlCQUFpQjtnREFBRXFCLFFBQVEsQ0FBQztnREFBSUMsR0FBRyxDQUFDOzRDQUFFLElBQUk7Z0RBQUVELFFBQVE7Z0RBQUdDLEdBQUc7NENBQUU7NENBQ3JFQyxZQUFZO2dEQUFFQyxVQUFVOzRDQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVF0Qyw4REFBQzVCLDBEQUFlQTs4QkFDYkksZ0NBQ0MsOERBQUNMLGlEQUFNQSxDQUFDZSxHQUFHO3dCQUNURCxXQUFVO3dCQUNWaUIsU0FBUzs0QkFBRVgsUUFBUTs0QkFBR1UsU0FBUzt3QkFBRTt3QkFDakNMLFNBQVM7NEJBQUVMLFFBQVE7NEJBQVFVLFNBQVM7d0JBQUU7d0JBQ3RDRSxNQUFNOzRCQUFFWixRQUFROzRCQUFHVSxTQUFTO3dCQUFFO3dCQUM5QkYsWUFBWTs0QkFBRUMsVUFBVTs0QkFBS0ksTUFBTTt3QkFBWTtrQ0FFL0MsNEVBQUNsQjs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDcEIsa0RBQUlBO3dDQUNIc0IsTUFBSzt3Q0FDTEYsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNakIsa0JBQWtCO2tEQUNsQzs7Ozs7O2tEQUdELDhEQUFDWixrREFBSUE7d0NBQ0hzQixNQUFLO3dDQUNMRixXQUFVO3dDQUNWUyxTQUFTLElBQU1qQixrQkFBa0I7a0RBQ2xDOzs7Ozs7a0RBR0QsOERBQUNaLGtEQUFJQTt3Q0FDSHNCLE1BQUs7d0NBQ0xGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTWpCLGtCQUFrQjtrREFDbEM7Ozs7OztrREFHRCw4REFBQ1osa0RBQUlBO3dDQUNIc0IsTUFBSzt3Q0FDTEYsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNakIsa0JBQWtCO2tEQUNsQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXbkI7QUFFQSxpRUFBZUosUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFN0YXJ0dXBcXFdlYnNpdGVcXHN0YXJ0dXBfd2Vic2l0ZVxcc3JjXFxjb21wb25lbnRzXFxOYXZiYXJcXE91dGVyTmF2LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcclxuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgSW5uZXJOYXYgfSBmcm9tIFwiLi9Jbm5lck5hdlwiO1xyXG5pbXBvcnQgeyBTaGltbWVyQnV0dG9uIH0gZnJvbSBcIi4uL3VpL3NoaW1tZXItYnV0dG9uXCI7XHJcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcclxuXHJcbmNvbnN0IE91dGVyTmF2ID0gKCkgPT4ge1xyXG4gIGNvbnN0IFtzY3JvbGxlZCwgc2V0U2Nyb2xsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFttb2JpbGVNZW51T3Blbiwgc2V0TW9iaWxlTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyBBZGQgc2Nyb2xsIGV2ZW50IGxpc3RlbmVyIHRvIGNoYW5nZSBuYXZiYXIgYXBwZWFyYW5jZSBvbiBzY3JvbGxcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlU2Nyb2xsID0gKCkgPT4ge1xyXG4gICAgICBzZXRTY3JvbGxlZCh3aW5kb3cuc2Nyb2xsWSA+IDEwKTtcclxuICAgIH07XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgaGFuZGxlU2Nyb2xsKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInNjcm9sbFwiLCBoYW5kbGVTY3JvbGwpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gVG9nZ2xlIG1vYmlsZSBtZW51XHJcbiAgY29uc3QgdG9nZ2xlTW9iaWxlTWVudSA9ICgpID0+IHtcclxuICAgIHNldE1vYmlsZU1lbnVPcGVuKCFtb2JpbGVNZW51T3Blbik7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxuYXZcclxuICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgdG9wLTAgbGVmdC0wIHctZnVsbCB6LTUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xyXG4gICAgICAgIHNjcm9sbGVkXHJcbiAgICAgICAgICA/IFwiYmFja2Ryb3AtYmx1ci1sZyBiZy10cmFuc3BhcmVudCBzaGFkb3ctc21cIlxyXG4gICAgICAgICAgOiBcImJhY2tkcm9wLWJsdXItc20gYmctdHJhbnNwYXJlbnRcIlxyXG4gICAgICB9YH1cclxuICAgID5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IG1kOnB4LTMyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gaC0xNiBtZDpoLTIwXCI+XHJcbiAgICAgICAgICB7LyogTG9nbyAqL31cclxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgc3JjPVwiL2ltYWdlcy9sb2dvLnBuZ1wiIC8vIFJlcGxhY2Ugd2l0aCB5b3VyIGxvZ28gcGF0aFxyXG4gICAgICAgICAgICAgIGFsdD1cIkNvbXBhbnkgTG9nb1wiXHJcbiAgICAgICAgICAgICAgd2lkdGg9ezEyMH1cclxuICAgICAgICAgICAgICBoZWlnaHQ9ezQwfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTYgdy1hdXRvXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtOFwiPlxyXG4gICAgICAgICAgICA8SW5uZXJOYXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogQm9vayBOb3cgQnV0dG9uIC0gSGlkZGVuIG9uIG1vYmlsZSAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrXCI+XHJcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY29udGFjdFwiPlxyXG4gICAgICAgICAgICAgIDxTaGltbWVyQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4+Qm9vayBGcmVlIENvbnN1bHRpbmc8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9TaGltbWVyQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogTW9iaWxlIE1lbnUgQnV0dG9uIHdpdGggQW5pbWF0aW9uICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW4gZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgaG92ZXI6dGV4dC1ibHVlLTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgcmVsYXRpdmUgdy02IGgtNlwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTW9iaWxlTWVudX1cclxuICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiVG9nZ2xlIG1lbnVcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCI+XHJcbiAgICAgICAgICAgICAgICA8bW90aW9uLnNwYW5cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgbGVmdC0wIGJsb2NrIHctNiBoLTAuNSBiZy1jdXJyZW50IHJvdW5kZWQtc20gdHJhbnNmb3JtLWdwdVwiXHJcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e21vYmlsZU1lbnVPcGVuID8geyByb3RhdGU6IDQ1LCB5OiA2IH0gOiB7IHJvdGF0ZTogMCwgeTogMCB9fVxyXG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8bW90aW9uLnNwYW5cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTMuNSBsZWZ0LTAgYmxvY2sgdy02IGgtMC41IGJnLWN1cnJlbnQgcm91bmRlZC1zbSB0cmFuc2Zvcm0tZ3B1XCJcclxuICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17bW9iaWxlTWVudU9wZW4gPyB7IG9wYWNpdHk6IDAgfSA6IHsgb3BhY2l0eTogMSB9fVxyXG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8bW90aW9uLnNwYW5cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTUgbGVmdC0wIGJsb2NrIHctNiBoLTAuNSBiZy1jdXJyZW50IHJvdW5kZWQtc20gdHJhbnNmb3JtLWdwdVwiXHJcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e21vYmlsZU1lbnVPcGVuID8geyByb3RhdGU6IC00NSwgeTogLTYgfSA6IHsgcm90YXRlOiAwLCB5OiAwIH19XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiB9fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEFuaW1hdGVkIE1vYmlsZSBOYXZpZ2F0aW9uIE1lbnUgKi99XHJcbiAgICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cclxuICAgICAgICAgIHttb2JpbGVNZW51T3BlbiAmJiAoXHJcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2IFxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBmaXhlZCBsZWZ0LTAgcmlnaHQtMCBvdmVyZmxvdy1oaWRkZW5cIlxyXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgaGVpZ2h0OiAwLCBvcGFjaXR5OiAwIH19XHJcbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBoZWlnaHQ6IFwiYXV0b1wiLCBvcGFjaXR5OiAxIH19XHJcbiAgICAgICAgICAgICAgZXhpdD17eyBoZWlnaHQ6IDAsIG9wYWNpdHk6IDAgfX1cclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMsIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2svOTAgYmFja2Ryb3AtYmx1ci1sZyBzaGFkb3ctbGcgcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBzcGFjZS15LTFcIj5cclxuICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2FnZW50c1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtY2VudGVyIHB5LTIgdGV4dC13aGl0ZSBob3Zlcjp0ZXh0LWJsdWUtMzAwIHRleHQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEFJIEFnZW50c1xyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9yZXNvdXJjZXNcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWNlbnRlciBweS0yIHRleHQtd2hpdGUgaG92ZXI6dGV4dC1ibHVlLTMwMCB0ZXh0LWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBSZXNvdXJjZXNcclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvYWJvdXRcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWNlbnRlciBweS0yIHRleHQtd2hpdGUgaG92ZXI6dGV4dC1ibHVlLTMwMCB0ZXh0LWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBBYm91dFxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9jb250YWN0XCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1jZW50ZXIgcHktMiB0ZXh0LXdoaXRlIGhvdmVyOnRleHQtYmx1ZS0zMDAgdGV4dC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgQ29udGFjdCBVc1xyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L25hdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgT3V0ZXJOYXY7Il0sIm5hbWVzIjpbIkxpbmsiLCJJbWFnZSIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSW5uZXJOYXYiLCJTaGltbWVyQnV0dG9uIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiT3V0ZXJOYXYiLCJzY3JvbGxlZCIsInNldFNjcm9sbGVkIiwibW9iaWxlTWVudU9wZW4iLCJzZXRNb2JpbGVNZW51T3BlbiIsImhhbmRsZVNjcm9sbCIsIndpbmRvdyIsInNjcm9sbFkiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInRvZ2dsZU1vYmlsZU1lbnUiLCJuYXYiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJhbmltYXRlIiwicm90YXRlIiwieSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsIm9wYWNpdHkiLCJpbml0aWFsIiwiZXhpdCIsImVhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar/OuterNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/navbar-menu.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/navbar-menu.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoveredLink: () => (/* binding */ HoveredLink),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   ProductItem: () => (/* binding */ ProductItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ MenuItem,Menu,ProductItem,HoveredLink auto */ \n\n\n\nconst transition = {\n    type: \"spring\",\n    mass: 0.5,\n    damping: 11.5,\n    stiffness: 100,\n    restDelta: 0.001,\n    restSpeed: 0.001\n};\nconst MenuItem = ({ setActive, active, item, children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseEnter: ()=>setActive(item),\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                transition: {\n                    duration: 0.3\n                },\n                className: \"cursor-pointer text-white hover:opacity-[0.9] py-2 px-6 rounded-full hover:bg-white/10 transition-colors\",\n                children: item\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            active !== null && children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.85,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.85,\n                    y: 10\n                },\n                transition: transition,\n                children: active === item && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-[calc(100%_+_1rem)] left-1/2 transform -translate-x-1/2 pt-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            scale: 0.8,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.8,\n                            opacity: 0\n                        },\n                        transition: transition,\n                        layoutId: \"active\",\n                        className: \"bg-black text-white backdrop-blur-sm rounded-2xl overflow-hidden border border-white/[0.2] shadow-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            layout: true,\n                            className: \"w-max h-full p-4\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\nconst Menu = ({ setActive, children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.nav, {\n        initial: {\n            backgroundColor: \"rgba(0,0,0,0)\"\n        },\n        whileHover: {\n            backgroundColor: \"rgba(0,0,0,1)\",\n            transition: {\n                duration: 0.2\n            }\n        },\n        onMouseLeave: ()=>setActive(null),\n        className: \"relative rounded-full border border-white/[0.015] bg-transparent text-white text-lg font-semibold flex justify-center px-2.5 py-2 w-fit transition-colors duration-200\",\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\nconst ProductItem = ({ title, description, href, src })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"flex space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: src,\n                alt: title,\n                width: 400,\n                height: 200,\n                className: \"h-full w-full object-cover transition-all duration-500 group-hover:scale-105\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-xl font-bold mb-1 text-black dark:text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-700 text-sm max-w-[10rem] dark:text-neutral-300\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\nconst HoveredLink = ({ children, ...rest })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        ...rest,\n        className: \"text-white hover:text-gray-300\",\n        onClick: (e)=>{\n            e.stopPropagation();\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/navbar-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/shimmer-button.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/shimmer-button.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShimmerButton: () => (/* binding */ ShimmerButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst ShimmerButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ shimmerColor = \"#ffffff\", shimmerSize = \"0.1em\", shimmerDuration = \"2.5s\", borderRadius = \"18px\", background = \"black\", hoverShimmerColor = \"#ffffff\", className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: {\n            \"--spread\": \"120deg\",\n            \"--shimmer-color\": shimmerColor,\n            \"--hover-shimmer-color\": hoverShimmerColor,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": background\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 px-4 py-2.5 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black font-semibold\", \"transform-gpu transition-all duration-300 ease-in-out active:translate-y-px hover:translate-y-[-3px]\", className),\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-z-30 blur-[2px]\", \"absolute inset-0 overflow-visible [container-type:size]\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0] group-hover:[background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--hover-shimmer-color)_var(--spread),transparent_var(--spread))]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"insert-0 absolute size-full\", \"rounded-2xl px-4 py-1.5 text-sm font-medium\", // transition\n                \"transform-gpu transition-all duration-300 ease-in-out\", \"group-hover:scale-[1.03]\" // Scale effect on hover\n                )\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)] transition-all duration-300\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n        lineNumber: 35,\n        columnNumber: 7\n    }, undefined);\n});\nShimmerButton.displayName = \"ShimmerButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/shimmer-button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXFN0YXJ0dXBcXFdlYnNpdGVcXHN0YXJ0dXBfd2Vic2l0ZVxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcclxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcclxuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXHJcbn1cclxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/motion","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/tailwind-merge","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();