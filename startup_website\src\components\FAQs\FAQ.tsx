"use client";

import React, { useState } from "react";
import { BlurFade } from "../ui/blur-fade";
import faqData from "../../data/faqs_home.json";

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleToggle = (idx: number) => {
    setOpenIndex(openIndex === idx ? null : idx);
  };

  return (
    <section className="relative md:pb-12 pb-4 overflow-hidden">
      <BlurFade direction="up" delay={0.2} offset={50} inViewMargin="-10%">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            FAQ
          </span>
        </div>

        <div className="container mx-auto">
          <div className="mb-8 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4">
              Frequently Asked Questions
            </h2>
            <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl pl-5 pr-5 leading-relaxed">
              Learn the answers to common questions about our AI solutions,
              tools, and services, helping you understand how they can benefit
              your business and streamline operations.
            </p>
          </div>
        </div>
      </BlurFade>

      <div className="w-[95%] md:w-[55%] mx-auto rounded-2xl mt-12">
        {faqData.map((faq, idx) => (
          <div
            key={idx}
            className={`transition-all duration-300 ${
              openIndex === idx
                ? "bg-neutral-900/80"
                : "hover:bg-neutral-800/60"
            } rounded-2xl mb-4`}
          >
            <button
              className="w-full flex justify-between items-center px-8 py-6 text-left focus:outline-none"
              onClick={() => handleToggle(idx)}
            >
              <span className="text-lg font-semibold text-white">
                {faq.question}
              </span>
              <span
                className={`text-3xl text-white/60 transform transition-transform duration-300 ${
                  openIndex === idx ? "rotate-45" : "rotate-0"
                }`}
              >
                +
              </span>
            </button>
            <div
              className={`px-8 overflow-hidden transition-all duration-300 ease-in-out ${
                openIndex === idx
                  ? "max-h-96 opacity-100 pb-6"
                  : "max-h-0 opacity-0 pb-0"
              }`}
            >
              <div className="text-lg text-white/80">{faq.answer}</div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FAQ;
