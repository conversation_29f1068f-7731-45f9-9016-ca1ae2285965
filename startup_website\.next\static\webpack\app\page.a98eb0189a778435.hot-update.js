"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx":
/*!**************************************************!*\
  !*** ./src/components/WhyAutomation/WhyAuto.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* harmony import */ var _Scroll__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Scroll */ \"(app-pages-browser)/./src/components/WhyAutomation/Scroll.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst WhyAuto = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 pt-24 md:pt-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"Why Automation?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4 md:pl-0 md:pr-0\",\n                                    children: \"Transform Your Business With Intelligent Automation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/[0.7] text-lg md:text-xl pl-5 pr-5 leading-relaxed md:pl-0 md:pr-0\",\n                                    children: \"Deploy AI-powered automation to eliminate inefficiencies, reduce costs, and create significant competitive advantages in your industry.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[95%] md:w-[90%] mx-auto mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Scroll__WEBPACK_IMPORTED_MODULE_3__.WhyAutomationDemo, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = WhyAuto;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhyAuto);\nvar _c;\n$RefreshReg$(_c, \"WhyAuto\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx\n"));

/***/ })

});