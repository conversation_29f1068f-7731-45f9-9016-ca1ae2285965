"use client";

import React from "react";
import { ValueCard } from "./Card";
import {
  IconAppWindow,
  IconBrain,
  IconCloud,
  IconCode,
  IconDeviceAnalytics,
  IconRocket,
} from "@tabler/icons-react";
import { BlurFade } from "../ui/blur-fade";

const Grid = () => {
  const cardData = [
    {
      title: "Innovation",
      description:
        "We push boundaries and embrace cutting-edge technologies to create solutions that transform industries.",
      icon: <IconRocket size={28} stroke={1.5} />,
    },
    {
      title: "Collaboration",
      description:
        "We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together.",
      icon: <IconBrain size={28} stroke={1.5} />,
    },
    {
      title: "Excellence",
      description:
        "We commit to delivering high-quality products that exceed expectations and stand the test of time.",
      icon: <IconDeviceAnalytics size={28} stroke={1.5} />,
    },
    {
      title: "User-Centered",
      description:
        "We design with empathy, putting users at the center of everything we create to solve real problems.",
      icon: <IconAppWindow size={28} stroke={1.5} />,
    },
    {
      title: "Adaptability",
      description:
        "We embrace change, continuously learn, and pivot quickly to stay ahead in a rapidly evolving landscape.",
      icon: <IconCloud size={28} stroke={1.5} />,
    },
    {
      title: "Impact",
      description:
        "We measure success by the positive difference our solutions make in people's lives and businesses.",
      icon: <IconCode size={28} stroke={1.5} />,
    },
  ];

  return (
    <section className="relative overflow-hidden bg-black">
      <BlurFade direction="up" delay={0.2} offset={30} inViewMargin="-10%">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-5 lg:gap-6 max-w-6xl mx-auto">
            {cardData.map((card, index) => (
              <BlurFade
                key={index}
                direction="up"
                delay={0.1 * index}
                offset={15}
                inViewMargin="-10%"
              >
                <ValueCard
                  title={card.title}
                  description={card.description}
                  icon={card.icon}
                />
              </BlurFade>
            ))}
          </div>
        </div>
      </BlurFade>
    </section>
  );
};

export default Grid;
