"use client";

import React from "react";
import { <PERSON>, Users, Target, Heart, TrendingUp } from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { BlurFade } from "../ui/blur-fade";

export function GlowingEffectDemo() {
  return (
    <ul className="grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-4 xl:max-h-[34rem] xl:grid-rows-2">
      <GridItem
        area="md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]"
        icon={<Rocket className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Innovation"
        description="We push boundaries and embrace cutting-edge technologies to create solutions that transform industries."
      />

      <GridItem
        area="md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]"
        icon={<Users className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Collaboration"
        description="We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together."
      />

      <GridItem
        area="md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]"
        icon={<Target className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Excellence"
        description="We commit to delivering high-quality products that exceed expectations and stand the test of time."
      />

      <GridItem
        area="md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]"
        icon={<Heart className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="User-Centered"
        description="We design with empathy, putting users at the center of everything we create to solve real problems."
      />

      <GridItem
        area="md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]"
        icon={<TrendingUp className="h-4 w-4 text-black dark:text-neutral-400" />}
        title="Impact"
        description="We measure success by the positive difference our solutions make in people's lives and businesses."
      />
    </ul>
  );
}

interface GridItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
}

const GridItem = ({ area, icon, title, description }: GridItemProps) => {
  return (
    <li className={`min-h-[14rem] list-none ${area}`}>
      <div className="relative h-full rounded-2xl border border-white/10 p-2 md:rounded-3xl md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 md:p-6 bg-black/50 backdrop-blur-sm">
          <div className="relative flex flex-1 flex-col justify-between gap-3">
            <div className="w-fit rounded-lg border border-gray-600 p-2">
              {icon}
            </div>
            <div className="space-y-3">
              <h3 className="-tracking-4 pt-0.5 font-manrope text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white">
                {title}
              </h3>
              <h2 className="font-dm-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold">
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};

const Grid = () => {
  return (
    <section className="relative overflow-hidden bg-black">
      <BlurFade direction="up" delay={0.2} offset={30} inViewMargin="-10%">
        <div className="container mx-auto px-4">
          <GlowingEffectDemo />
        </div>
      </BlurFade>
    </section>
  );
};

export default Grid;
