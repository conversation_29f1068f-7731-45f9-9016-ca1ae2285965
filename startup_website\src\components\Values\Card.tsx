"use client";
import React from "react";
import { cn } from "@/lib/utils";

interface CardProps {
  title: string;
  description: string;
  price?: string;
  icon?: React.ReactNode;
  className?: string;
}

export function ValueCard({
  title,
  description,
  price,
  icon,
  className,
}: CardProps) {
  return (
    <div
      className={cn(
        "rounded-xl h-full p-4 sm:p-5 bg-black/80 border border-white/10 backdrop-blur-sm",
        "shadow-md hover:shadow-lg transition-all duration-300",
        "group overflow-hidden relative",
        className
      )}
    >
      {icon && (
        <div
          className="mb-3 text-white bg-black/30 p-2 rounded-lg inline-block 
          shadow-inner transform transition-transform duration-300 group-hover:scale-110 
          ring-1 ring-white/10 group-hover:ring-white/20"
        >
          {icon}
        </div>
      )}

      <h3
        className="text-lg sm:text-xl font-semibold mt-2 mb-2 text-white group-hover:text-white/90 
        transition-colors"
      >
        {title}
      </h3>

      <p className="text-xs sm:text-sm text-white/70 leading-relaxed">
        {description}
      </p>

      {price && (
        <button
          className="mt-4 rounded-full pl-4 pr-2 py-1.5 text-white flex items-center space-x-2 
          bg-blue-600 text-xs font-medium
          transform transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg"
        >
          <span>Buy now </span>
          <span className="bg-black/30 rounded-full px-2 py-0.5 text-white ml-2">
            ${price}
          </span>
        </button>
      )}
    </div>
  );
}

// Keep the original component for backward compatibility
export function BackgroundGradientDemo() {
  return (
    <div>
      <ValueCard
        title="Air Jordan 4 Retro Reimagined"
        description="The Air Jordan 4 Retro Reimagined Bred will release on Saturday, February 17, 2024. Your best opportunity to get these right now is by entering raffles and waiting for the official releases."
        price="100"
      />
    </div>
  );
}
