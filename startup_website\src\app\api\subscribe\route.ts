import { NextRequest, NextResponse } from "next/server";
import nodemailer from "nodemailer";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // Validate email
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      return NextResponse.json(
        { error: "Please provide a valid email address" },
        { status: 400 }
      );
    }

    // Configure transporter
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || "smtp.gmail.com",
      port: Number(process.env.EMAIL_PORT) || 587,
      secure: process.env.EMAIL_SECURE === "true",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Email recipient (your email address)
    const recipientEmail = process.env.RECIPIENT_EMAIL;

    // Current date and time for record
    const timestamp = new Date().toISOString();

    // Email content - using consistent subject line to maintain thread
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: recipientEmail,
      subject: "Newsletter Subscription Tracking", // Consistent subject creates a thread in most email clients
      text: `New subscriber: ${email} (${timestamp})`,
      html: `
        <h2>New Newsletter Subscription</h2>
        <p>Subscription timestamp: ${timestamp}</p>
        <p><strong>Email:</strong> ${email}</p>
      `,
      // References and In-Reply-To headers help maintain the thread
      headers: {
        References: "<<EMAIL>>",
        "In-Reply-To": "<<EMAIL>>",
      },
    };

    // Send confirmation email to subscriber
    const confirmationMailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Thank you for subscribing!",
      text: `Thank you for subscribing to our newsletter. You'll receive updates on new services and announcements.`,
      html: `
        <h2>Thank you for subscribing!</h2>
        <p>You have successfully subscribed to our newsletter.</p>
        <p>You'll receive updates on new services and announcements.</p>
      `,
    };

    // Send emails
    await transporter.sendMail(mailOptions);
    await transporter.sendMail(confirmationMailOptions);

    return NextResponse.json(
      { message: "Subscribed successfully!" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Subscription error:", error);
    return NextResponse.json(
      { error: "Failed to subscribe. Please try again." },
      { status: 500 }
    );
  }
}
