"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Agents/Agents.tsx":
/*!******************************************!*\
  !*** ./src/components/Agents/Agents.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _Carousel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Carousel */ \"(app-pages-browser)/./src/components/Agents/Carousel.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Agents = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_5__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"AI Agents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4\",\n                                    children: \"AI Agents Built for Every Workflow\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-8 text-white/[0.7] text-lg md:text-xl px-5 md:px-0 leading-relaxed\",\n                                    children: \"Our AI agents integrate effortlessly into your existing systems, handling everything from routine tasks to complex processes. Experience a seamless, intelligent transformation that empowers your business to work smarter, faster, and more effectively than ever before.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/agents\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-6 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                        children: \"Explore All Agents\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Carousel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16 md:mt-24 w-full h-screen relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    src: \"/images/flowchart.png\",\n                    alt: \"AI Agents Workflow Flowchart\",\n                    fill: true,\n                    className: \"object-cover\",\n                    style: {\n                        objectFit: 'contain'\n                    },\n                    priority: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Agents;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Agents);\nvar _c;\n$RefreshReg$(_c, \"Agents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Agents/Agents.tsx\n"));

/***/ })

});