"use client";

import React from "react";
import TeamCard from "./TeamCard";

const team = [
  {
    name: "<PERSON><PERSON><PERSON>",
    title: "Co-Founder & CEO",
    image: "/images/team/akshat.png",
    link: "https://www.linkedin.com/in/akshat3144/",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    title: "Co-Founder & CTO",
    image: "/images/team/raghav.png",
    link: "https://www.linkedin.com/in/raghav-sarna-4789bb2b3/",
  },
];

const Team: React.FC = () => {
  return (
    <section className="relative pb-24 overflow-hidden">
      <div className="text-center mb-6">
        <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
          Team
        </span>
      </div>

      <div className="container mx-auto mb-16">
        <div className="mb-8 max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 px-4 md:px-0">
            One team to build AI to solve the challenges of tomorrow
          </h2>
        </div>
      </div>

      <div className="max-w-3xl mx-auto grid grid-cols-1 sm:grid-cols-2 gap-6 justify-items-center">
        {team.map((member) => (
          <TeamCard key={member.name} {...member} />
        ))}
      </div>
    </section>
  );
};

export default Team;
