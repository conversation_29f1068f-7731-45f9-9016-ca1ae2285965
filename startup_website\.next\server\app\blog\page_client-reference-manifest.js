globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/blog/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/Footer/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar/OuterNav.tsx":{"*":{"id":"(ssr)/./src/components/Navbar/OuterNav.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutUs/AboutUs.tsx":{"*":{"id":"(ssr)/./src/components/AboutUs/AboutUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutUs/Video.tsx":{"*":{"id":"(ssr)/./src/components/AboutUs/Video.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/JoinUs/JoinUs.tsx":{"*":{"id":"(ssr)/./src/components/JoinUs/JoinUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Team/Team.tsx":{"*":{"id":"(ssr)/./src/components/Team/Team.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Values/Values.tsx":{"*":{"id":"(ssr)/./src/components/Values/Values.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Blog/BlogSection.tsx":{"*":{"id":"(ssr)/./src/components/Blog/BlogSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Resources/Resources.tsx":{"*":{"id":"(ssr)/./src/components/Resources/Resources.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Agents/AgentsSection.tsx":{"*":{"id":"(ssr)/./src/components/Agents/AgentsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Agents/Agents.tsx":{"*":{"id":"(ssr)/./src/components/Agents/Agents.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Benefits/Benefits.tsx":{"*":{"id":"(ssr)/./src/components/Benefits/Benefits.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FAQs/FAQ.tsx":{"*":{"id":"(ssr)/./src/components/FAQs/FAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Features/Features.tsx":{"*":{"id":"(ssr)/./src/components/Features/Features.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Hero/Hero.tsx":{"*":{"id":"(ssr)/./src/components/Hero/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx":{"*":{"id":"(ssr)/./src/components/WhyAutomation/WhyAuto.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Startup\\Website\\startup_website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-manrope\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"]}],\"variableName\":\"manrope\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-manrope\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"]}],\"variableName\":\"manrope\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"DM_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-dm-sans\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"dmSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"DM_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-dm-sans\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"dmSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Footer\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Navbar\\OuterNav.tsx":{"id":"(app-pages-browser)/./src/components/Navbar/OuterNav.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\AboutUs\\AboutUs.tsx":{"id":"(app-pages-browser)/./src/components/AboutUs/AboutUs.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\AboutUs\\Video.tsx":{"id":"(app-pages-browser)/./src/components/AboutUs/Video.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\JoinUs\\JoinUs.tsx":{"id":"(app-pages-browser)/./src/components/JoinUs/JoinUs.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Team\\Team.tsx":{"id":"(app-pages-browser)/./src/components/Team/Team.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Values\\Values.tsx":{"id":"(app-pages-browser)/./src/components/Values/Values.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Blog\\BlogSection.tsx":{"id":"(app-pages-browser)/./src/components/Blog/BlogSection.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Resources\\Resources.tsx":{"id":"(app-pages-browser)/./src/components/Resources/Resources.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/blog/page","static/chunks/app/blog/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/blog/page","static/chunks/app/blog/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/blog/page","static/chunks/app/blog/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/blog/page","static/chunks/app/blog/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Agents\\AgentsSection.tsx":{"id":"(app-pages-browser)/./src/components/Agents/AgentsSection.tsx","name":"*","chunks":[],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Agents\\Agents.tsx":{"id":"(app-pages-browser)/./src/components/Agents/Agents.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Benefits\\Benefits.tsx":{"id":"(app-pages-browser)/./src/components/Benefits/Benefits.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\FAQs\\FAQ.tsx":{"id":"(app-pages-browser)/./src/components/FAQs/FAQ.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Features\\Features.tsx":{"id":"(app-pages-browser)/./src/components/Features/Features.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\Hero\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/Hero/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Startup\\Website\\startup_website\\src\\components\\WhyAutomation\\WhyAuto.tsx":{"id":"(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false}},"entryCSSFiles":{"D:\\Startup\\Website\\startup_website\\src\\":[],"D:\\Startup\\Website\\startup_website\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Startup\\Website\\startup_website\\src\\app\\page":[],"D:\\Startup\\Website\\startup_website\\src\\app\\blog\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer/Footer.tsx":{"*":{"id":"(rsc)/./src/components/Footer/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar/OuterNav.tsx":{"*":{"id":"(rsc)/./src/components/Navbar/OuterNav.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutUs/AboutUs.tsx":{"*":{"id":"(rsc)/./src/components/AboutUs/AboutUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutUs/Video.tsx":{"*":{"id":"(rsc)/./src/components/AboutUs/Video.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/JoinUs/JoinUs.tsx":{"*":{"id":"(rsc)/./src/components/JoinUs/JoinUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Team/Team.tsx":{"*":{"id":"(rsc)/./src/components/Team/Team.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Values/Values.tsx":{"*":{"id":"(rsc)/./src/components/Values/Values.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Blog/BlogSection.tsx":{"*":{"id":"(rsc)/./src/components/Blog/BlogSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Resources/Resources.tsx":{"*":{"id":"(rsc)/./src/components/Resources/Resources.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Agents/AgentsSection.tsx":{"*":{"id":"(rsc)/./src/components/Agents/AgentsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Agents/Agents.tsx":{"*":{"id":"(rsc)/./src/components/Agents/Agents.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Benefits/Benefits.tsx":{"*":{"id":"(rsc)/./src/components/Benefits/Benefits.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FAQs/FAQ.tsx":{"*":{"id":"(rsc)/./src/components/FAQs/FAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Features/Features.tsx":{"*":{"id":"(rsc)/./src/components/Features/Features.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Hero/Hero.tsx":{"*":{"id":"(rsc)/./src/components/Hero/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx":{"*":{"id":"(rsc)/./src/components/WhyAutomation/WhyAuto.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}