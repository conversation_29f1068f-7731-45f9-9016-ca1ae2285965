"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import blogPosts from "@/data/blog-posts.json";

interface BlogPost {
  id: string;
  title: string;
  excerpt?: string;
  image: string;
  category: string;
  author?: string;
  authorImage?: string;
  date: string;
  slug: string;
}

const BlogSection: React.FC = () => {
  // Use the imported data
  const featuredPosts: BlogPost[] = blogPosts;

  return (
    <section className="relative pb-24 pt-16 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="mb-12">
          <span className="text-lg text-white/70 mb-2 block">News & Blogs</span>
          <h2 className="text-4xl md:text-5xl lg:text-6xl">Latest Articles</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          {/* Featured/Main Article - takes up 3/5 of the width on desktop */}
          <div className="lg:col-span-3 relative rounded-xl overflow-hidden group">
            <Link
              href={`/blog/${featuredPosts[0].slug}`}
              className="block h-full"
            >
              <div className="relative h-[400px] w-full overflow-hidden">
                <Image
                  src={featuredPosts[0].image}
                  alt={featuredPosts[0].title}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent"></div>
              </div>

              <div className="absolute bottom-0 left-0 p-6 md:p-8 w-full">
                <span className="inline-block text-sm px-3 py-1 bg-white/10 backdrop-blur-sm rounded-lg mb-4">
                  {featuredPosts[0].category}
                </span>
                <h3 className="text-2xl md:text-3xl font-semibold mb-4">
                  {featuredPosts[0].title}
                </h3>

                <div className="flex items-center">
                  {featuredPosts[0].authorImage && (
                    <div className="relative h-8 w-8 rounded-full overflow-hidden mr-3">
                      <Image
                        src={featuredPosts[0].authorImage}
                        alt={featuredPosts[0].author || ""}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}

                  <div className="flex items-center">
                    {featuredPosts[0].author && (
                      <span className="text-sm text-white/90 mr-2">
                        by {featuredPosts[0].author}
                      </span>
                    )}
                    <span className="text-sm text-white/70">
                      — {featuredPosts[0].date}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* Side Articles - takes up 2/5 of the width on desktop */}
          <div className="lg:col-span-2 space-y-6">
            {featuredPosts.slice(1).map((post) => (
              <Link
                key={post.id}
                href={`/blog/${post.slug}`}
                className="group flex gap-4 items-start hover:bg-white/5 rounded-xl p-3 transition-colors"
              >
                <div className="relative h-24 w-24 min-w-[96px] overflow-hidden rounded-lg">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>

                <div className="flex-1">
                  <span className="text-xs text-white/70 mb-1 block">
                    {post.category}
                  </span>
                  <h4 className="text-base font-medium leading-tight mb-1">
                    {post.title}
                  </h4>
                </div>
              </Link>
            ))}

            <Link
              href="/blog"
              className="inline-block text-blue-400 hover:text-blue-300 transition-colors mt-4 text-sm font-medium"
            >
              See all Posts
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
