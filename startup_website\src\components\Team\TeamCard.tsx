import Image from 'next/image';

type TeamMemberProps = {
  name: string;
  title: string;
  image: string;
  link: string;
};

const TeamCard = ({ name, title, image, link }: TeamMemberProps) => {
  return (
    <div className="relative w-full max-w-xs overflow-hidden rounded-2xl shadow-lg group transition duration-300">
      <Image
        src={image}
        alt={name}
        width={400}
        height={500}
        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent flex flex-col justify-end p-4">
        <h3 className="text-white text-lg font-semibold">{name}</h3>
        <p className="text-white text-sm">{title}</p>
        <a href={link} className="text-white text-sm mt-2 inline-flex items-center hover:underline">
          Learn more <span className="ml-1">→</span>
        </a>
      </div>
    </div>
  );
};

export default TeamCard;
