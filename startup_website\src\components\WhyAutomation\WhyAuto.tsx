"use client";

import React from "react";
import { BlurFade } from "../ui/blur-fade";
import { WhyAutomationDemo } from "./Scroll";

const WhyAuto: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 pt-24 md:pt-32 overflow-hidden">
      <BlurFade direction="up" delay={0.2} offset={50} inViewMargin="-10%">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            Why Automation?
          </span>
        </div>

        <div className="container mx-auto">
          <div className="mb-8 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4 md:pl-0 md:pr-0">
              Transform Your Business With Intelligent Automation
            </h2>
            <p className="text-white/[0.7] text-lg md:text-xl pl-5 pr-5 leading-relaxed md:pl-0 md:pr-0">
              Deploy AI-powered automation to eliminate inefficiencies, reduce costs, and create significant competitive advantages in your industry.
            </p>
          </div>
        </div>
      </BlurFade>

      <div className="w-[95%] md:w-[90%] mx-auto mt-12">
        <WhyAutomationDemo />
      </div>
    </section>
  );
};

export default WhyAuto;
