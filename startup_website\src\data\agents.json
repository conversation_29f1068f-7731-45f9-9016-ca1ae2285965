[{"id": 1, "category": "Data Extraction", "title": "Data Entry AI Agent", "workflows": ["Catalog Entry", "ID & Document Extraction"], "image": "images/agents/1.png"}, {"id": 2, "category": "Customer Service", "title": "Customer Experience AI Agent", "workflows": ["Loyalty Program Management", "Customer Feedback Processing", "FAQ Assistance"], "image": "images/agents/2.png"}, {"id": 3, "category": "Financial Services & Banking", "title": "Financial Compliance and Reporting AI Agent", "workflows": ["Financial Reporting", "Credit Analysis"], "image": "images/agents/3.png"}, {"id": 4, "category": "Financial Services & Banking", "title": "Billing Support AI Agent", "workflows": ["Billings & Renewals", "Billing Support", "Subscription Requests Handling"], "image": "images/agents/4.png"}, {"id": 5, "category": "Healthcare", "title": "Medical Records AI Agent", "workflows": ["Patient Data Processing", "Medical History Analysis"], "image": "images/agents/5.png"}, {"id": 6, "category": "E-commerce", "title": "Order Processing AI Agent", "workflows": ["Order Fulfillment", "Return Management", "Inventory Updates"], "image": "images/agents/6.png"}, {"id": 7, "category": "Legal", "title": "Contract Analysis AI Agent", "workflows": ["Contract Review", "Compliance Checking", "Risk Assessment"], "image": "images/agents/7.png"}, {"id": 8, "category": "Human Resources", "title": "Recruitment AI Agent", "workflows": ["Resume Screening", "Candidate Matching", "Interview Scheduling"], "image": "images/agents/8.png"}, {"id": 9, "category": "Marketing", "title": "Content Generation AI Agent", "workflows": ["Social Media Content", "Email Campaigns", "Blog Writing"], "image": "images/agents/9.png"}, {"id": 10, "category": "Support", "title": "Technical Support AI Agent", "workflows": ["Ticket Prioritization", "Troubleshooting Guides", "Knowledge Base Creation"], "image": "images/agents/10.png"}, {"id": 11, "category": "Insurance", "title": "Claims Processing AI Agent", "workflows": ["Claims Validation", "<PERSON>aud Detection", "Payment Processing"], "image": "images/agents/11.png"}, {"id": 12, "category": "Education", "title": "Learning Assistant AI Agent", "workflows": ["Question Answering", "Study Material Generation", "Progress Tracking"], "image": "images/agents/12.png"}]