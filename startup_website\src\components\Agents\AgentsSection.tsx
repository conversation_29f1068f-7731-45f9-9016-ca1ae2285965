"use client";

import { useState } from "react";
import Card from "./Card";
import { FiSearch } from "react-icons/fi";
import agentsData from "../../data/agents.json";
import { BlurFade } from "../ui/blur-fade";

interface Agent {
  id: number;
  category: string;
  title: string;
  workflows: string[];
  image: string;
}

const AgentsSection = () => {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter agents based on search term
  const filteredAgents = agentsData.filter(
    (agent) =>
      agent.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.workflows.some((workflow) =>
        workflow.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  return (
    <section className="relative pt-32 overflow-hidden">
      <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
      <div className="text-center mb-6">
        <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
          AI Agents
        </span>
      </div>

      <div className="container mx-auto">
        <div className="mb-8 max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6">
            AI Agents Marketplace
          </h2>
          <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed">
            Discover our collection of specialized AI agents designed to
            streamline your workflow and boost productivity across different
            business functions.
          </p>
        </div>
      </div>
      

      {/* Search Bar */}
      <div className="max-w-md mx-auto mb-8 md:mb-12 px-12 relative">
        <div className="relative">
          <input
            type="text"
            placeholder="Search agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 sm:px-5 py-2.5 sm:py-3 pr-10 sm:pr-12 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 transition text-sm sm:text-base"
            aria-label="Search agents"
          />
          <FiSearch className="absolute right-3 sm:right-4 top-1/2 -translate-y-1/2 text-gray-400 text-lg sm:text-xl" />
        </div>
      </div>
      </BlurFade>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-16 md:px-32 px-6 py-4">
        {filteredAgents.map((agent: Agent) => (
          <Card
            key={agent.id}
            category={agent.category}
            title={agent.title}
            workflows={agent.workflows}
            image={agent.image}
          />
        ))}

        {filteredAgents.length === 0 && (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500">
              No agents found matching your search criteria.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default AgentsSection;
