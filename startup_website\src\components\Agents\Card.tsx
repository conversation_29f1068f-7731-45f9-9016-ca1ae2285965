import Image from "next/image";
import { useState } from "react";

interface CardProps {
  category: string;
  title: string;
  workflows: string[];
  image: string;
}

const Card = ({ category, title, workflows, image }: CardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  // Limit workflows to 3 maximum
  const displayWorkflows = workflows.slice(0, 3);
  const hasMoreWorkflows = workflows.length > 3;

  return (
    <div
      className="bg-gradient-to-b from-gray-900 to-black rounded-2xl overflow-hidden shadow-lg h-[400px] text-white transition-all duration-300 hover:shadow-2xl flex flex-col"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        transform: isHovered ? "translateY(-8px)" : "translateY(0)",
      }}
    >
      <div className="relative h-40 w-full overflow-hidden flex-shrink-0">
        <Image
          src={`/${image}`}
          alt={title}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
          className={`object-cover transition-transform duration-700 ${
            isHovered ? "scale-110" : "scale-100"
          }`}
        />
        <div className="absolute inset-0"></div>
        <div className="absolute bottom-3 left-3">
          <span
            className={`text-xs text-white/[0.8] bg-[#211f22] px-3 py-2 rounded-lg font-semibold uppercase tracking-wide shadow-lg`}
          >
            {category}
          </span>
        </div>
      </div>

      <div className="p-5 flex-grow flex flex-col">
        <h2 className="text-xl font-bold leading-tight mb-4 line-clamp-2">
          {title}
        </h2>
        <div className="space-y-3 flex-grow">
          <p className="text-xs font-bold text-white/[0.8] uppercase tracking-wider">
            Workflows
          </p>
          <ul className="space-y-2.5">
            {displayWorkflows.map((workflow, idx) => (
              <li
                key={idx}
                className="bg-[#0d0d0d] text-white/[0.8] backdrop-blur-sm rounded-lg px-3 py-2 text-sm transition-colors hover:bg-[#1a1a1a]"
              >
                <span className="line-clamp-1">{workflow}</span>
              </li>
            ))}
            {hasMoreWorkflows && (
              <li className="text-xs text-gray-400 pl-2 italic mt-1.5">
                +{workflows.length - 3} more workflows
              </li>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Card;
