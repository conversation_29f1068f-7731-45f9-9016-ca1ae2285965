"use client";

import React from "react";
import { BlurFade } from "../ui/blur-fade";

const Resources: React.FC = () => {
  return (
    <section className="relative pt-32 overflow-hidden">
      <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            News & Blogs
          </span>
        </div>

        <div className="container mx-auto">
          <div className="mb-8 max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4">
              Let's Talk AI, Automation, and Everything in Between
            </h2>
            <p className="text-white/[0.7] text-lg md:text-xl max-w-3xl mx-auto pl-5 pr-5 leading-relaxed">
              Stay ahead of the curve with insightful blogs on AI, automation, and how they're transforming the world. Curious minds, this is your place to explore and learn!
            </p>
          </div>
        </div>
        </BlurFade>
    </section>
  );
};

export default Resources;