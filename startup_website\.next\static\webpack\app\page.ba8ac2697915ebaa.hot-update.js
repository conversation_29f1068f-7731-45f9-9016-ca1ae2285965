"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Hero/Text.tsx":
/*!**************************************!*\
  !*** ./src/components/Hero/Text.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _FlipWord__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FlipWord */ \"(app-pages-browser)/./src/components/Hero/FlipWord.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n\n\n\n\n\nconst HeroTextSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center px-6 md:px-12 lg:px-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__.BlurFade, {\n                    direction: \"up\",\n                    delay: 0.0,\n                    offset: 50,\n                    inViewMargin: \"-10%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-6xl lg:text-7xl font-semibold text-white leading-tight mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlipWord__WEBPACK_IMPORTED_MODULE_2__.FlipWord, {}, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__.BlurFade, {\n                    direction: \"up\",\n                    delay: 0.1,\n                    offset: 50,\n                    inViewMargin: \"-10%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto mb-12 leading-relaxed\",\n                        children: \"Harness the next generation of agentic AI designed to think, act, and adapt like an expert. Our solutions automate processes, optimize operations, and empower teams to focus on what matters most — driving growth, innovation, and sustainable success.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__.BlurFade, {\n                    direction: \"up\",\n                    delay: 0.2,\n                    offset: 50,\n                    inViewMargin: \"-10%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/about\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-8 py-4 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                children: \"Learn More\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeroTextSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroTextSection);\nvar _c;\n$RefreshReg$(_c, \"HeroTextSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0hlcm8vVGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ1k7QUFDVDtBQUNjO0FBRTNDLE1BQU1JLGtCQUFrQjtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNILG1EQUFRQTtvQkFBQ0ksV0FBVTtvQkFBS0MsT0FBTztvQkFBS0MsUUFBUTtvQkFBSUMsY0FBYTs4QkFDOUQsNEVBQUNDO3dCQUFHTCxXQUFVO2tDQUNaLDRFQUFDTCwrQ0FBUUE7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLWCw4REFBQ0UsbURBQVFBO29CQUFDSSxXQUFVO29CQUFLQyxPQUFPO29CQUFLQyxRQUFRO29CQUFJQyxjQUFhOzhCQUM5RCw0RUFBQ0U7d0JBQUVOLFdBQVU7a0NBQThFOzs7Ozs7Ozs7Ozs4QkFNM0YsOERBQUNILG1EQUFRQTtvQkFBQ0ksV0FBVTtvQkFBS0MsT0FBTztvQkFBS0MsUUFBUTtvQkFBSUMsY0FBYTs4QkFDOUQsNEVBQUNMO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDSixrREFBSUE7NEJBQUNXLE1BQUs7c0NBQ1QsNEVBQUNDO2dDQUFPUixXQUFVOzBDQUFpSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTL0w7S0E5Qk1GO0FBZ0NOLGlFQUFlQSxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJEOlxcU3RhcnR1cFxcV2Vic2l0ZVxcc3RhcnR1cF93ZWJzaXRlXFxzcmNcXGNvbXBvbmVudHNcXEhlcm9cXFRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgRmxpcFdvcmQgfSBmcm9tIFwiLi9GbGlwV29yZFwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcbmltcG9ydCB7IEJsdXJGYWRlIH0gZnJvbSBcIi4uL3VpL2JsdXItZmFkZVwiO1xyXG5cclxuY29uc3QgSGVyb1RleHRTZWN0aW9uID0gKCkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTYgbWQ6cHgtMTIgbGc6cHgtMTZcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYXgtdy00eGxcIj5cclxuICAgICAgICA8Qmx1ckZhZGUgZGlyZWN0aW9uPVwidXBcIiBkZWxheT17MC4wfSBvZmZzZXQ9ezUwfSBpblZpZXdNYXJnaW49XCItMTAlXCI+XHJcbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNnhsIGxnOnRleHQtN3hsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBsZWFkaW5nLXRpZ2h0IG1iLThcIj5cclxuICAgICAgICAgIDxGbGlwV29yZCAvPlxyXG4gICAgICAgIDwvaDE+XHJcbiAgICAgICAgPC9CbHVyRmFkZT5cclxuXHJcbiAgICAgICAgey8qIENlbnRlcmVkIHN1YmhlYWRpbmcgd2l0aCBncmFkaWVudCB0ZXh0ICovfVxyXG4gICAgICAgIDxCbHVyRmFkZSBkaXJlY3Rpb249XCJ1cFwiIGRlbGF5PXswLjF9IG9mZnNldD17NTB9IGluVmlld01hcmdpbj1cIi0xMCVcIj5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlL1swLjddIHRleHQtbGcgbWQ6dGV4dC14bCBtYXgtdy0yeGwgbXgtYXV0byBtYi0xMiBsZWFkaW5nLXJlbGF4ZWRcIj5cclxuICAgICAgICAgIEhhcm5lc3MgdGhlIG5leHQgZ2VuZXJhdGlvbiBvZiBhZ2VudGljIEFJIGRlc2lnbmVkIHRvIHRoaW5rLCBhY3QsIGFuZCBhZGFwdCBsaWtlIGFuIGV4cGVydC4gT3VyIHNvbHV0aW9ucyBhdXRvbWF0ZSBwcm9jZXNzZXMsIG9wdGltaXplIG9wZXJhdGlvbnMsIGFuZCBlbXBvd2VyIHRlYW1zIHRvIGZvY3VzIG9uIHdoYXQgbWF0dGVycyBtb3N0IOKAlCBkcml2aW5nIGdyb3d0aCwgaW5ub3ZhdGlvbiwgYW5kIHN1c3RhaW5hYmxlIHN1Y2Nlc3MuXHJcbiAgICAgICAgPC9wPlxyXG4gICAgICAgIDwvQmx1ckZhZGU+XHJcblxyXG4gICAgICAgIHsvKiBDZW50ZXJlZCBDYWxsLXRvLUFjdGlvbiBCdXR0b24gKi99XHJcbiAgICAgICAgPEJsdXJGYWRlIGRpcmVjdGlvbj1cInVwXCIgZGVsYXk9ezAuMn0gb2Zmc2V0PXs1MH0gaW5WaWV3TWFyZ2luPVwiLTEwJVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiPlxyXG4gICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTggcHktNCBiZy13aGl0ZSB0ZXh0LWJsYWNrIGZvbnQtbWVkaXVtIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLXdoaXRlL1swLjg1XSBob3ZlcjpzaGFkb3ctbWQgaG92ZXI6dHJhbnNsYXRlLXktWy00cHhdXCI+XHJcbiAgICAgICAgICAgICAgTGVhcm4gTW9yZVxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L0JsdXJGYWRlPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBIZXJvVGV4dFNlY3Rpb247XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkZsaXBXb3JkIiwiTGluayIsIkJsdXJGYWRlIiwiSGVyb1RleHRTZWN0aW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGlyZWN0aW9uIiwiZGVsYXkiLCJvZmZzZXQiLCJpblZpZXdNYXJnaW4iLCJoMSIsInAiLCJocmVmIiwiYnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Hero/Text.tsx\n"));

/***/ })

});