"use client";

import Link from "next/link";
import Image from "next/image";
import { ArrowRightIcon } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative overflow-hidden bg-gradient-to-b from-black to-gray-900 pt-24 pb-12">
      Decorative Elements
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      <div className="absolute -top-24 -right-24 h-64 w-64 rounded-full bg-blue-500/10 blur-3xl"></div>
      <div className="absolute -bottom-32 -left-32 h-64 w-64 rounded-full bg-purple-500/10 blur-3xl"></div>
      <div className="container relative mx-auto px-6 md:px-8 lg:px-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 gap-16 sm:grid-cols-2 md:grid-cols-12">
          {/* Company Info */}
          <div className="md:col-span-4">
            <Link href="/" className="inline-block">
              <Image
                src="/images/logo.webp"
                alt="Company Logo"
                width={140}
                height={48}
                className="h-12 w-auto"
              />
            </Link>
            <p className="mt-6 text-base leading-relaxed text-white/70">
              Building intelligent solutions that transform how businesses
              operate through advanced AI and cutting-edge technology.
            </p>
            <div className="mt-8 flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.name}
                  className="group flex h-10 w-10 items-center justify-center rounded-full border border-white/10 bg-white/5 text-white/70 backdrop-blur-sm transition-all duration-300 hover:border-white/30 hover:bg-white/10 hover:text-white hover:shadow-lg hover:shadow-white/5"
                >
                  <span className="transform transition-transform duration-300 group-hover:scale-110">
                    {social.icon}
                  </span>
                </a>
              ))}
            </div>
          </div>

          {/* Links Section */}
          <div className="grid md:col-span-5 md:grid-cols-3 gap-12">
            {/* Products */}
            <div>
              <h3 className="mb-5 text-lg font-semibold text-white after:mt-2 after:block after:h-0.5 after:w-8 after:bg-blue-500/80">
                Products
              </h3>
              <ul className="space-y-3">
                {productLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="group flex items-center text-sm text-white/60 transition-colors hover:text-white"
                    >
                      <span className="mr-2 h-1 w-0 rounded-full bg-blue-500 transition-all duration-300 group-hover:w-3"></span>
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Resources */}
            <div>
              <h3 className="mb-5 text-lg font-semibold text-white after:mt-2 after:block after:h-0.5 after:w-8 after:bg-purple-500/80">
                Resources
              </h3>
              <ul className="space-y-3">
                {resourceLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="group flex items-center text-sm text-white/60 transition-colors hover:text-white"
                    >
                      <span className="mr-2 h-1 w-0 rounded-full bg-purple-500 transition-all duration-300 group-hover:w-3"></span>
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="mb-5 text-lg font-semibold text-white after:mt-2 after:block after:h-0.5 after:w-8 after:bg-teal-500/80">
                Company
              </h3>
              <ul className="space-y-3">
                {companyLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="group flex items-center text-sm text-white/60 transition-colors hover:text-white"
                    >
                      <span className="mr-2 h-1 w-0 rounded-full bg-teal-500 transition-all duration-300 group-hover:w-3"></span>
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Newsletter */}
          <div className="md:col-span-3">
            <h3 className="mb-5 text-lg font-semibold text-white after:mt-2 after:block after:h-0.5 after:w-8 after:bg-indigo-500/80">
              Stay Connected
            </h3>
            <p className="mb-5 text-base text-white/70">
              Subscribe to receive updates on new features and announcements.
            </p>
            <form className="relative">
              <div className="overflow-hidden rounded-lg bg-white/5 p-0.5 backdrop-blur-sm">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full bg-black/30 py-3 px-4 text-white placeholder:text-white/40 focus:outline-none"
                  required
                />
                <div className="h-px w-full bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
                <button
                  type="submit"
                  className="mt-4 flex w-full items-center justify-center rounded-md bg-gradient-to-r from-blue-600 to-indigo-600 py-3 px-4 text-sm font-medium text-white transition-all hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg hover:shadow-indigo-500/20"
                >
                  <span>Subscribe</span>
                  <ArrowRightIcon size={16} className="ml-2" />
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-16 pt-8 border-t border-white/10">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <p className="text-sm text-white/50">
              © {currentYear} Your Company. All rights reserved.
            </p>
            <div className="flex space-x-8">
              <Link
                href="/privacy"
                className="text-sm text-white/50 transition-colors hover:text-white/80"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-sm text-white/50 transition-colors hover:text-white/80"
              >
                Terms of Service
              </Link>
              <Link
                href="/cookies"
                className="text-sm text-white/50 transition-colors hover:text-white/80"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

// Social media icons
const socialLinks = [
  {
    name: "Twitter",
    href: "https://twitter.com",
    icon: (
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
      </svg>
    ),
  },
  {
    name: "LinkedIn",
    href: "https://linkedin.com",
    icon: (
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6.5 21.5h-5v-13h5v13zM4 6.5C2.5 6.5 1.5 5.3 1.5 4s1-2.4 2.5-2.4c1.6 0 2.5 1 2.6 2.5 0 1.4-1 2.5-2.6 2.5zm11.5 6c-1 0-2 1-2 2v7h-5v-13h5v1.8c.8-1 2.1-1.8 3.5-1.8 2.5 0 5 2.1 5 6.1V21.5h-5V14.5c0-1-1-2-2-2z" />
      </svg>
    ),
  },
  {
    name: "GitHub",
    href: "https://github.com",
    icon: (
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
        <path
          fillRule="evenodd"
          d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
          clipRule="evenodd"
        />
      </svg>
    ),
  },
  {
    name: "Instagram",
    href: "https://instagram.com",
    icon: (
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
        <path
          fillRule="evenodd"
          d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
          clipRule="evenodd"
        />
      </svg>
    ),
  },
];

// Footer link sections
const productLinks = [
  { name: "AI Automation", href: "/products/ai-automation" },
  { name: "Workflow Tools", href: "/products/workflow" },
  { name: "Data Analytics", href: "/products/analytics" },
  { name: "Custom Solutions", href: "/products/custom" },
];

const resourceLinks = [
  { name: "Blog", href: "/blog" },
  { name: "Documentation", href: "/docs" },
  { name: "Case Studies", href: "/case-studies" },
  { name: "Help Center", href: "/help" },
];

const companyLinks = [
  { name: "About Us", href: "/about" },
  { name: "Careers", href: "/careers" },
  { name: "Contact", href: "/contact" },
  { name: "Partners", href: "/partners" },
];

export default Footer;
