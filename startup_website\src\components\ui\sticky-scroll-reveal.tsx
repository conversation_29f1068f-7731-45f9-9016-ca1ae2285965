"use client";
import React, { useEffect, useRef, useState } from "react";
import { useMotionValueEvent, useScroll } from "motion/react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";

// Define proper types for content
type ContentItem = {
  title: string;
  description: string;
  content?: React.ReactNode;
};

export const StickyScroll = ({
  content,
  contentClassName,
  autoScroll = true,
  autoScrollInterval = 2000,
  pauseOnHover = true,
}: {
  content: ContentItem[];
  contentClassName?: string;
  autoScroll?: boolean;
  autoScrollInterval?: number;
  pauseOnHover?: boolean;
}) => {
  const [activeCard, setActiveCard] = React.useState(0);
  const ref = useRef<HTMLDivElement>(null);
  const autoScrollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const { scrollYProgress } = useScroll({
    container: ref,
    offset: ["start start", "end start"],
  });
  const cardLength = content.length;

  // new state to track if the container is in view
  const [inView, setInView] = useState(false);

  // IntersectionObserver to detect when the component comes into view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );
    if (ref.current) {
      observer.observe(ref.current);
    }
    return () => {
      observer.disconnect();
    };
  }, []);

  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    const cardsBreakpoints = content.map((_, index) => index / cardLength);
    const closestBreakpointIndex = cardsBreakpoints.reduce(
      (acc, breakpoint, index) => {
        const distance = Math.abs(latest - breakpoint);
        if (distance < Math.abs(latest - cardsBreakpoints[acc])) {
          return index;
        }
        return acc;
      },
      0
    );
    setActiveCard(closestBreakpointIndex);
  });



  // Auto-scroll functionality; now only active when the component is in view.
  useEffect(() => {
    // Activate auto-scroll only on desktop view (>= 768px) and when in view
    if (autoScroll && !isPaused && window.innerWidth >= 768 && inView) {
      autoScrollIntervalRef.current = setInterval(() => {
        if (ref.current) {
          const nextCard = (activeCard + 1) % cardLength;
          const scrollHeight =
            ref.current.scrollHeight - ref.current.clientHeight;
          const segmentPosition = (nextCard / (cardLength - 1)) * scrollHeight;

          ref.current.scrollTo({
            top: segmentPosition,
            behavior: "smooth",
          });
        }
      }, autoScrollInterval);
    }

    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
    };
  }, [
    activeCard,
    autoScroll,
    autoScrollInterval,
    cardLength,
    isPaused,
    inView,
  ]);

  const handleMouseEnter = () => {
    if (pauseOnHover) {
      setIsPaused(true);
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      setIsPaused(false);
    }
  };

  return (
    <div
      className="relative flex h-[35rem] justify-between overflow-y-auto rounded-md p-6 md:p-10 bg-transparent scrollbar-hide"
      style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
      ref={ref}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <style jsx global>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .progress-indicator {
          transition: height 0.3s ease;
        }
      `}</style>

      {/* Fixed Position Progress indicator */}
      <div className="sticky top-0 left-0 h-full w-2 z-10">
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 h-[70%] flex flex-col justify-between py-4">
          <div className="relative h-full w-[3px] bg-gray-700/30 rounded-full">
            {content.map((_, index) => (
              <div
                key={`progress-${index}`}
                className={`absolute w-[5px] left-[-1px] rounded-full transition-all duration-300 ${
                  index === activeCard ? "bg-white" : "bg-gray-400/40"
                }`}
                style={{
                  top: `${(index * 100) / (content.length - 1)}%`,
                  height: index === activeCard ? "30px" : "10px",
                  transform: "translateY(-50%)",
                }}
                onClick={() => {
                  if (ref.current) {
                    const scrollHeight =
                      ref.current.scrollHeight - ref.current.clientHeight;
                    const segmentPosition =
                      (index / (content.length - 1)) * scrollHeight;
                    ref.current.scrollTo({
                      top: segmentPosition,
                      behavior: "smooth",
                    });
                  }
                }}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="div relative flex items-start px-6 md:px-10">
        <div className="max-w-2xl">
          {content.map((item, index) => (
            <motion.div
              key={item.title + index}
              className="my-24 md:my-28"
              initial={{ opacity: 0 }}
              animate={{
                opacity: activeCard === index ? 1 : 0.25,
                y: activeCard === index ? 0 : 10,
                scale: activeCard === index ? 1 : 0.95,
              }}
              transition={{ duration: 0.4 }}
            >
              <motion.h2 className="text-2xl md:text-3xl font-bold text-slate-100">
                {item.title}
              </motion.h2>
              <motion.p className="text-lg mt-6 max-w-sm text-slate-200/90 leading-relaxed">
                {item.description}
              </motion.p>
            </motion.div>
          ))}
          <div className="h-32" />
        </div>
      </div>

      {/* Centered image container - 1:1 aspect ratio */}
      <div className="sticky top-0 right-0 h-full hidden lg:flex items-center justify-center">
        <motion.div
          className={cn(
            "h-96 w-96 overflow-hidden rounded-2xl shadow-xl bg-transparent",
            contentClassName
          )}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          whileHover={{
            scale: 1.05,
            rotateY: 5,
            transition: { duration: 0.3, ease: "easeOut" }
          }}
        >
          <div className="relative z-10 h-full w-full">
            {content[activeCard].content ? (
              content[activeCard].content
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <span className="text-xl font-semibold text-white opacity-80">
                  {content[activeCard].title}
                </span>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
