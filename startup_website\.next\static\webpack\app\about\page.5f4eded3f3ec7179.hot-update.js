"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n            key: \"1xq2db\"\n        }\n    ]\n];\nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zap\", __iconNode);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Values/Grid.tsx":
/*!****************************************!*\
  !*** ./src/components/Values/Grid.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlowingEffectDemo: () => (/* binding */ GlowingEffectDemo),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/glowing-effect */ \"(app-pages-browser)/./src/components/ui/glowing-effect.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ GlowingEffectDemo,default auto */ \n\n\n\n\nfunction GlowingEffectDemo() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Innovation\",\n                description: \"We push boundaries and embrace cutting-edge technologies to create solutions that transform industries.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Collaboration\",\n                description: \"We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Excellence\",\n                description: \"We commit to delivering high-quality products that exceed expectations and stand the test of time.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 15\n                }, void 0),\n                title: \"User-Centered\",\n                description: \"We design with empathy, putting users at the center of everything we create to solve real problems.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Adaptability\",\n                description: \"We embrace change, continuously learn, and pivot quickly to stay ahead in a rapidly evolving landscape.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Impact\",\n                description: \"We measure success by the positive difference our solutions make in people's lives and businesses.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = GlowingEffectDemo;\nconst GridItem = (param)=>{\n    let { area, icon, title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"min-h-[14rem] list-none \".concat(area),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-full rounded-2xl border border-white/10 p-2 md:rounded-3xl md:p-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__.GlowingEffect, {\n                    spread: 40,\n                    glow: true,\n                    disabled: false,\n                    proximity: 64,\n                    inactiveZone: 0.01\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 md:p-6 bg-black/50 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1 flex-col justify-between gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-fit rounded-lg border border-gray-600 p-2\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"-tracking-4 pt-0.5 font-manrope text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-dm-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = GridItem;\nconst Grid = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__.BlurFade, {\n            direction: \"up\",\n            delay: 0.2,\n            offset: 30,\n            inViewMargin: \"-10%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlowingEffectDemo, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = Grid;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Grid);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GlowingEffectDemo\");\n$RefreshReg$(_c1, \"GridItem\");\n$RefreshReg$(_c2, \"Grid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Values/Grid.tsx\n"));

/***/ })

});