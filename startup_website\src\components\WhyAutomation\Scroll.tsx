"use client";
import React from "react";
import Image from "next/image";
import { StickyScroll } from "../ui/sticky-scroll-reveal";

const content = [
  {
    title: "Boost Operational Efficiency",
    description:
      "Automation eliminates manual repetitive tasks and reduces human error, allowing your business to operate at maximum efficiency. Our AI solutions handle routine processes with precision, ensuring consistent quality and freeing your team to focus on high-value work.",
    content: (
      <div className="flex h-full w-full items-center justify-center text-white">
        <div className="relative w-full h-full group cursor-pointer">
          <Image
            src="/images/why-auto/1.png"
            alt="operational efficiency visualization"
            fill
            className="object-cover rounded-xl transition-all duration-500 ease-out group-hover:scale-110 group-hover:rotate-2"
            style={{ objectFit: 'cover' }}
            priority
            key="efficiency-image-1"
          />
        </div>
      </div>
    ),
  },
  {
    title: "Scale Without Adding Headcount",
    description:
      "As your business grows, automation scales with you without proportionally increasing costs. AI agents work 24/7 without fatigue, handling increased workloads seamlessly. This allows you to expand operations and enter new markets without the traditional staffing constraints.",
    content: (
      <div className="flex h-full w-full items-center justify-center text-white">
        <div className="relative w-full h-full group cursor-pointer">
          <Image
            src="/images/why-auto/2.png"
            alt="business scaling visualization"
            fill
            className="object-cover rounded-xl transition-all duration-500 ease-out group-hover:scale-110 group-hover:rotate-2"
            style={{ objectFit: 'cover' }}
            priority
            key="scaling-image-2"
          />
        </div>
      </div>
    ),
  },
  {
    title: "Gain Competitive Advantage",
    description:
      "In today's fast-paced market, speed and agility determine success. Automated workflows respond instantly to changing conditions, analyze data in real-time, and execute decisions faster than manual processes ever could. Stay ahead of competitors by delivering superior response times and customer experiences.",
    content: (
      <div className="flex h-full w-full items-center justify-center text-white">
        <div className="relative w-full h-full group cursor-pointer">
          <Image
            src="/images/why-auto/3.png"
            alt="competitive advantage graph"
            fill
            className="object-cover rounded-xl transition-all duration-500 ease-out group-hover:scale-110 group-hover:rotate-2"
            style={{ objectFit: 'cover' }}
            priority
            key="competitive-image-3"
            unoptimized
          />
        </div>
      </div>
    ),
  },
  {
    title: "Enhance Decision Making",
    description:
      "AI automation doesn't just execute tasks—it provides valuable insights by analyzing patterns across vast datasets. Gain access to actionable intelligence that would be impossible to discover manually, enabling smarter, data-driven decisions that improve business outcomes.",
    content: (
      <div className="flex h-full w-full items-center justify-center text-white">
        <div className="relative w-full h-full group cursor-pointer">
          <Image
            src="/images/why-auto/4.png"
            alt="data visualization dashboard"
            fill
            className="object-cover rounded-xl transition-all duration-500 ease-out group-hover:scale-110 group-hover:rotate-2"
            style={{ objectFit: 'cover' }}
            priority
            key="decision-image-4"
          />
        </div>
      </div>
    ),
  },
];

export function WhyAutomationDemo() {
  return (
    <div className="w-full backdrop-blur-sm mx-auto">
      <StickyScroll
        content={content}
        contentClassName="backdrop-blur-md bg-transparent rounded-2xl overflow-hidden"
      />
    </div>
  );
}
