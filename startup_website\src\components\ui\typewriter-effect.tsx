"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

export const TypewriterEffect = ({
  words,
  className,
  cursorClassName,
}: {
  words: string[];
  className?: string;
  cursorClassName?: string;
}) => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [currentText, setCurrentText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const typingSpeed = isDeleting ? 50 : 100; // Faster when deleting
    const pauseDelay = 1500; // Pause when word is complete
    const currentWord = words[currentWordIndex];

    // Handle typing/deleting logic
    const handleTyping = () => {
      setCurrentText((prev) => {
        if (isDeleting) {
          // Deleting text
          return prev.substring(0, prev.length - 1);
        } else {
          // Typing text
          return currentWord.substring(0, prev.length + 1);
        }
      });
    };

    let timeout: NodeJS.Timeout;

    // All text is typed, pause before deleting
    if (!isDeleting && currentText === currentWord) {
      timeout = setTimeout(() => setIsDeleting(true), pauseDelay);
    }
    // Text is fully deleted, move to next word
    else if (isDeleting && currentText === "") {
      setIsDeleting(false);
      setCurrentWordIndex((prev) => (prev + 1) % words.length);
    }
    // Continue typing/deleting
    else {
      timeout = setTimeout(handleTyping, typingSpeed);
    }

    return () => clearTimeout(timeout);
  }, [words, currentWordIndex, currentText, isDeleting]);

  return (
    <span className={cn("inline-block", className)}>
      {currentText}
      <span
        className={cn(
          "animate-blink ml-1 inline-block h-10 w-[2px] bg-white",
          cursorClassName
        )}
      >
        &nbsp;
      </span>
    </span>
  );
};
