"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/Values/Grid.tsx":
/*!****************************************!*\
  !*** ./src/components/Values/Grid.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlowingEffectDemo: () => (/* binding */ GlowingEffectDemo),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/glowing-effect */ \"(app-pages-browser)/./src/components/ui/glowing-effect.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ GlowingEffectDemo,default auto */ \n\n\n\n\nfunction GlowingEffectDemo() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Innovation\",\n                description: \"We push boundaries and embrace cutting-edge technologies to create solutions that transform industries.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Collaboration\",\n                description: \"We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Excellence\",\n                description: \"We commit to delivering high-quality products that exceed expectations and stand the test of time.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 15\n                }, void 0),\n                title: \"User-Centered\",\n                description: \"We design with empathy, putting users at the center of everything we create to solve real problems.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Adaptability\",\n                description: \"We embrace change, continuously learn, and pivot quickly to stay ahead in a rapidly evolving landscape.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Impact\",\n                description: \"We measure success by the positive difference our solutions make in people's lives and businesses.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = GlowingEffectDemo;\nconst GridItem = (param)=>{\n    let { icon, title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-[14rem] group\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-full rounded-2xl border border-white/10 p-3 hover:border-white/20 transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__.GlowingEffect, {\n                    spread: 40,\n                    glow: true,\n                    disabled: false,\n                    proximity: 64,\n                    inactiveZone: 0.01\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 flex h-full flex-col gap-4 p-6 bg-black/30 backdrop-blur-sm rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-fit rounded-lg border border-white/20 p-3 bg-white/5\",\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-manrope text-xl md:text-2xl font-semibold text-white\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-dm-sans text-sm md:text-base text-white/70 leading-relaxed\",\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = GridItem;\nconst Grid = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__.BlurFade, {\n            direction: \"up\",\n            delay: 0.2,\n            offset: 30,\n            inViewMargin: \"-10%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlowingEffectDemo, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = Grid;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Grid);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GlowingEffectDemo\");\n$RefreshReg$(_c1, \"GridItem\");\n$RefreshReg$(_c2, \"Grid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Values/Grid.tsx\n"));

/***/ })

});