/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CAgents%5C%5CAgents.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CBenefits%5C%5CBenefits.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFAQs%5C%5CFAQ.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CHero%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CWhyAutomation%5C%5CWhyAuto.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CAgents%5C%5CAgents.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CBenefits%5C%5CBenefits.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFAQs%5C%5CFAQ.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CHero%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CWhyAutomation%5C%5CWhyAuto.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Agents/Agents.tsx */ \"(app-pages-browser)/./src/components/Agents/Agents.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Benefits/Benefits.tsx */ \"(app-pages-browser)/./src/components/Benefits/Benefits.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FAQs/FAQ.tsx */ \"(app-pages-browser)/./src/components/FAQs/FAQ.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Features/Features.tsx */ \"(app-pages-browser)/./src/components/Features/Features.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero/Hero.tsx */ \"(app-pages-browser)/./src/components/Hero/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WhyAutomation/WhyAuto.tsx */ \"(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1N0YXJ0dXAlNUMlNUNXZWJzaXRlJTVDJTVDc3RhcnR1cF93ZWJzaXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q0FnZW50cyU1QyU1Q0FnZW50cy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTdGFydHVwJTVDJTVDV2Vic2l0ZSU1QyU1Q3N0YXJ0dXBfd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNCZW5lZml0cyU1QyU1Q0JlbmVmaXRzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1N0YXJ0dXAlNUMlNUNXZWJzaXRlJTVDJTVDc3RhcnR1cF93ZWJzaXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q0ZBUXMlNUMlNUNGQVEudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU3RhcnR1cCU1QyU1Q1dlYnNpdGUlNUMlNUNzdGFydHVwX3dlYnNpdGUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDRmVhdHVyZXMlNUMlNUNGZWF0dXJlcy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTdGFydHVwJTVDJTVDV2Vic2l0ZSU1QyU1Q3N0YXJ0dXBfd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNIZXJvJTVDJTVDSGVyby50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTdGFydHVwJTVDJTVDV2Vic2l0ZSU1QyU1Q3N0YXJ0dXBfd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNXaHlBdXRvbWF0aW9uJTVDJTVDV2h5QXV0by50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQTRJO0FBQzVJO0FBQ0Esc01BQWdKO0FBQ2hKO0FBQ0Esb0xBQXVJO0FBQ3ZJO0FBQ0Esc01BQWdKO0FBQ2hKO0FBQ0Esc0xBQXdJO0FBQ3hJO0FBQ0EsOE1BQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcU3RhcnR1cFxcXFxXZWJzaXRlXFxcXHN0YXJ0dXBfd2Vic2l0ZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxBZ2VudHNcXFxcQWdlbnRzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFN0YXJ0dXBcXFxcV2Vic2l0ZVxcXFxzdGFydHVwX3dlYnNpdGVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcQmVuZWZpdHNcXFxcQmVuZWZpdHMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcU3RhcnR1cFxcXFxXZWJzaXRlXFxcXHN0YXJ0dXBfd2Vic2l0ZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxGQVFzXFxcXEZBUS50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJEOlxcXFxTdGFydHVwXFxcXFdlYnNpdGVcXFxcc3RhcnR1cF93ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEZlYXR1cmVzXFxcXEZlYXR1cmVzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFN0YXJ0dXBcXFxcV2Vic2l0ZVxcXFxzdGFydHVwX3dlYnNpdGVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcSGVyb1xcXFxIZXJvLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFN0YXJ0dXBcXFxcV2Vic2l0ZVxcXFxzdGFydHVwX3dlYnNpdGVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcV2h5QXV0b21hdGlvblxcXFxXaHlBdXRvLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CAgents%5C%5CAgents.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CBenefits%5C%5CBenefits.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFAQs%5C%5CFAQ.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CHero%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CWhyAutomation%5C%5CWhyAuto.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FAQs/FAQ.tsx":
/*!*************************************!*\
  !*** ./src/components/FAQs/FAQ.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* harmony import */ var _data_faqs_home_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../data/faqs_home.json */ \"(app-pages-browser)/./src/data/faqs_home.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FAQ = ()=>{\n    _s();\n    const [openIndex, setOpenIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleToggle = (idx)=>{\n        setOpenIndex(openIndex === idx ? null : idx);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative md:pb-12 pb-4 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"FAQ\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4\",\n                                children: \"Frequently Asked Questions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.3,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-[95%] md:w-[55%] mx-auto rounded-2xl mt-12\",\n                    children: _data_faqs_home_json__WEBPACK_IMPORTED_MODULE_3__.map((faq, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-all duration-300 \".concat(openIndex === idx ? \"bg-neutral-900/80\" : \"hover:bg-neutral-800/60\", \" rounded-2xl mb-4\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full flex justify-between items-center px-8 py-6 text-left focus:outline-none\",\n                                    onClick: ()=>handleToggle(idx),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: faq.question\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-3xl text-white/60 transform transition-transform duration-300 \".concat(openIndex === idx ? \"rotate-45\" : \"rotate-0\"),\n                                            children: \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-8 overflow-hidden transition-all duration-300 ease-in-out \".concat(openIndex === idx ? \"max-h-96 opacity-100 pb-6\" : \"max-h-0 opacity-0 pb-0\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-white/80\",\n                                        children: faq.answer\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FAQ, \"7z1SfW1ag/kVV/D8SOtFgmPOJ8o=\");\n_c = FAQ;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FAQ);\nvar _c;\n$RefreshReg$(_c, \"FAQ\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FAQs/FAQ.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/faqs_home.json":
/*!*********************************!*\
  !*** ./src/data/faqs_home.json ***!
  \*********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"question":"What is agentic AI?","answer":"Agentic AI refers to self-learning AI systems that autonomously make decisions and execute tasks, adapting dynamically to changes in workflows."},{"question":"How does AI automation improve operational efficiency?","answer":"AI automation reduces manual, repetitive work and human error, enabling faster and more consistent execution of business processes."},{"question":"Can your AI integrate with our existing software?","answer":"Yes, our AI solutions are designed to seamlessly integrate with most popular business tools and platforms without disrupting your current workflows."},{"question":"Is my company data secure with your AI systems?","answer":"We prioritize data security using encryption, access controls, and compliance with industry standards to ensure your information remains protected."},{"question":"How quickly can we expect to see results after implementation?","answer":"Many clients observe efficiency gains and cost reductions within weeks of deploying AI solutions, depending on the complexity of workflows automated."},{"question":"Do you offer customization for specific business needs?","answer":"Absolutely. Our AI agents can be tailored to match your unique processes, goals, and industry requirements."}]');

/***/ })

});