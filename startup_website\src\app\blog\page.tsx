import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";
import blogData from "@/data/blogs.json";

export const metadata: Metadata = {
  title: "Blog | Your Company",
  description:
    "Latest articles, news and insights about AI technology and solutions",
};

// Define a type for our blog posts
type BlogPost = {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  category: string;
  date: string;
  slug: string;
};

export default function BlogPage() {
  // Use the imported JSON data
  const blogs: BlogPost[] = blogData;

  return (
    <main className="min-h-screen bg-black text-white pt-24">
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            News & Blogs
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-16 max-w-[80%] flex justify-center mx-auto">
          {blogs.map((post) => (
            <Link href={`/blog/${post.slug}`} key={post.id} className="group">
              <div className="rounded-xl overflow-hidden bg-zinc-900 border border-white/10 h-full flex flex-col hover:border-blue-500/50 transition-colors duration-300">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div className="p-6 flex flex-col flex-grow">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-xs px-2 py-1 bg-white/10 rounded-md">
                      {post.category}
                    </span>
                    <span className="text-xs text-white/60">{post.date}</span>
                  </div>
                  <h2 className="text-xl font-semibold mb-3 group-hover:text-blue-400 transition-colors">
                    {post.title}
                  </h2>
                  <p className="text-white/70 text-sm mb-4 line-clamp-3 flex-grow">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center text-blue-400 group-hover:text-blue-300 transition-colors mt-auto">
                    <span className="text-sm">Read article</span>
                    <svg
                      className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </main>
  );
}
