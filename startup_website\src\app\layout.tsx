import type { Metadata } from "next";
import { Manrope, DM_Sans } from "next/font/google";
import "./globals.css";

import Navbar from "@/components/Navbar/OuterNav";
import Footer from "@/components/Footer/Footer";

// Load Manrope for headings
const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-manrope",
  weight: ["300", "400", "500", "600", "700", "800"],
});

// Load DM Sans for body text and subheadings
const dmSans = DM_Sans({
  subsets: ["latin"],
  variable: "--font-dm-sans",
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${manrope.variable} ${dmSans.variable} antialiased bg-black text-white`}
      >
        <Navbar />
        {children}
        <div className="pt-24">
          <Footer />
        </div>
      </body>
    </html>
  );
}
