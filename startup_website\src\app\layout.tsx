import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";

import Navbar from "@/components/Navbar/OuterNav";
import Footer from "@/components/Footer/Footer";

// Load Product Sans from your local directory
const productSans = localFont({
  src: [
    {
      path: "./Product_Sans_Regular.ttf",
      weight: "400",
      style: "normal",
    }
  ],
  variable: "--font-product-sans",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${productSans.variable} antialiased bg-black text-white`}
      >
        <Navbar />
        {children}
        <div className="pt-24">
          <Footer />
        </div>
      </body>
    </html>
  );
}
