"use client";

import React from "react";

const Join: React.FC = () => {
  return (
    <section
      className="relative pb-24 md:pb-32 pt-24 md:pt-32 overflow-hidden bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('/images/joinus.png')",
        backgroundSize: "cover",
      }}
    >
      {/* Add a dark overlay to make text readable */}
      <div className="absolute inset-0 bg-black/50 z-0"></div>

      {/* Gradient overlay that fades to black at the bottom */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/70 to-black z-0"></div>

      {/* Content with higher z-index to appear above the overlay */}
      <div className="relative z-10">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            Join Us
          </span>
        </div>

        <div className="container mx-auto md:mb-16 mb-8">
          <div className="mb-8 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 px-4 md:px-0">
              Be Part of Something Extraordinary
            </h2>
            <p className="mb-8 text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed px-5 md:px-0">
              Ready to shape the future of agentic AI? Join a team where your ideas matter, your growth is our priority, and every day is an opportunity to innovate.
            </p>
            <a href="mailto:<EMAIL>">
            <button className="px-6 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]">
              Email Us
            </button>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Join;
