"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Agents/Agents.tsx":
/*!******************************************!*\
  !*** ./src/components/Agents/Agents.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _Carousel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Carousel */ \"(app-pages-browser)/./src/components/Agents/Carousel.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Agents = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_5__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"AI Agents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4\",\n                                    children: \"AI Agents Built for Every Workflow\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-8 text-white/[0.7] text-lg md:text-xl px-5 md:px-0 leading-relaxed\",\n                                    children: \"Our AI agents integrate effortlessly into your existing systems, handling everything from routine tasks to complex processes. Experience a seamless, intelligent transformation that empowers your business to work smarter, faster, and more effectively than ever before.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/agents\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-6 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                        children: \"Explore All Agents\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Carousel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16 md:mt-24 w-full relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    src: \"/images/flowchart.png\",\n                    alt: \"AI Agents Workflow Flowchart\",\n                    width: 1200,\n                    height: 800,\n                    className: \"w-full h-auto\",\n                    priority: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Agents\\\\Agents.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Agents;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Agents);\nvar _c;\n$RefreshReg$(_c, \"Agents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Agents/Agents.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Benefits/Benefits.tsx":
/*!**********************************************!*\
  !*** ./src/components/Benefits/Benefits.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Benefits = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-[95%] md:w-[55%] mx-auto rounded-2xl mt-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: \"/images/benefits.png\",\n                alt: \"AI operational benefits illustration\",\n                className: \"w-full h-auto rounded-xl shadow-lg\",\n                width: 1200,\n                height: 800,\n                priority: false\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Benefits;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Benefits);\nvar _c;\n$RefreshReg$(_c, \"Benefits\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Benefits/Benefits.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FAQs/FAQ.tsx":
/*!*************************************!*\
  !*** ./src/components/FAQs/FAQ.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* harmony import */ var _data_faqs_home_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../data/faqs_home.json */ \"(app-pages-browser)/./src/data/faqs_home.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FAQ = ()=>{\n    _s();\n    const [openIndex, setOpenIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleToggle = (idx)=>{\n        setOpenIndex(openIndex === idx ? null : idx);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative md:pb-12 pb-4 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"FAQ\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4\",\n                                children: \"Frequently Asked Questions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[95%] md:w-[55%] mx-auto rounded-2xl mt-12\",\n                children: _data_faqs_home_json__WEBPACK_IMPORTED_MODULE_3__.map((faq, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"transition-all duration-300 \".concat(openIndex === idx ? \"bg-neutral-900/80\" : \"hover:bg-neutral-800/60\", \" rounded-2xl mb-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full flex justify-between items-center px-8 py-6 text-left focus:outline-none\",\n                                onClick: ()=>handleToggle(idx),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: faq.question\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl text-white/60 transform transition-transform duration-300 \".concat(openIndex === idx ? \"rotate-45\" : \"rotate-0\"),\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-8 overflow-hidden transition-all duration-300 ease-in-out \".concat(openIndex === idx ? \"max-h-96 opacity-100 pb-6\" : \"max-h-0 opacity-0 pb-0\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg text-white/80\",\n                                    children: faq.answer\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\FAQs\\\\FAQ.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FAQ, \"7z1SfW1ag/kVV/D8SOtFgmPOJ8o=\");\n_c = FAQ;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FAQ);\nvar _c;\n$RefreshReg$(_c, \"FAQ\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FAQs/FAQ.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Features/Features.tsx":
/*!**********************************************!*\
  !*** ./src/components/Features/Features.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Bento__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Bento */ \"(app-pages-browser)/./src/components/Features/Bento.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Features = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 px-4 md:px-0\",\n                                    children: \"You Stay in Control. We’ll Handle the Busywork.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto px-5 md:px-0 leading-relaxed\",\n                                    children: \"From seamless communication to streamlined scheduling, our AI-driven tools work quietly in the background—so you can focus on leading, building, and scaling. Explore the smart systems that make it all happen effortlessly.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[95%] md:w-[80%] mx-auto rounded-2xl bg-black/30 border border-white/10 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Bento__WEBPACK_IMPORTED_MODULE_2__.Bento, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Features\\\\Features.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Features;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Features);\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Features/Features.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Hero/Hero.tsx":
/*!**************************************!*\
  !*** ./src/components/Hero/Hero.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Text */ \"(app-pages-browser)/./src/components/Hero/Text.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst Hero = ()=>{\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            // Trigger fade-in animation after component mounts\n            const timer = setTimeout({\n                \"Hero.useEffect.timer\": ()=>{\n                    setIsLoaded(true);\n                }\n            }[\"Hero.useEffect.timer\"], 100);\n            return ({\n                \"Hero.useEffect\": ()=>clearTimeout(timer)\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-32 md:h-0\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Hero.tsx\",\n                lineNumber: 20,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:min-h-screen flex items-center justify-center relative transition-opacity duration-1000 ease-in-out \".concat(isLoaded ? 'opacity-100' : 'opacity-0'),\n                style: {\n                    backgroundImage: 'url(/images/landing-bg.png)',\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/40\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Hero.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Text__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Hero.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Hero.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Hero.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Hero, \"e/1lVN3R6kIvuSIAmUIHNmZXQsc=\");\n_c = Hero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Hero/Hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Hero/Text.tsx":
/*!**************************************!*\
  !*** ./src/components/Hero/Text.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _FlipWord__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FlipWord */ \"(app-pages-browser)/./src/components/Hero/FlipWord.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n\n\n\n\n\nconst HeroTextSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center px-6 md:px-12 lg:px-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold text-white leading-tight mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlipWord__WEBPACK_IMPORTED_MODULE_2__.FlipWord, {}, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__.BlurFade, {\n                    direction: \"up\",\n                    delay: 0.1,\n                    offset: 50,\n                    inViewMargin: \"-10%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto mb-12 leading-relaxed\",\n                        children: \"Harness the next generation of agentic AI designed to think, act, and adapt like an expert. Our solutions automate processes, optimize operations, and empower teams to focus on what matters most — driving growth, innovation, and sustainable success.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-full sm:flex-row gap-5 items-center justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/about\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:px-6 px-4 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                children: \"Learn More\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/contact\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:hidden px-4 py-3 bg-transparent text-white font-medium border rounded-lg transition-all duration-300 cursor-pointer hover:shadow-md hover:translate-y-[-4px]\",\n                                children: \"Book Free Consulting\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeroTextSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroTextSection);\nvar _c;\n$RefreshReg$(_c, \"HeroTextSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Hero/Text.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx":
/*!**************************************************!*\
  !*** ./src/components/WhyAutomation/WhyAuto.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* harmony import */ var _Scroll__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Scroll */ \"(app-pages-browser)/./src/components/WhyAutomation/Scroll.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst WhyAuto = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 pt-24 md:pt-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                direction: \"up\",\n                delay: 0.2,\n                offset: 50,\n                inViewMargin: \"-10%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl\",\n                            children: \"Why Automation?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4 md:pl-0 md:pr-0\",\n                                    children: \"Transform Your Business With Intelligent Automation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/[0.7] text-lg md:text-xl pl-5 pr-5 leading-relaxed md:pl-0 md:pr-0\",\n                                    children: \"Deploy AI-powered automation to eliminate inefficiencies, reduce costs, and create significant competitive advantages in your industry.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[95%] md:w-[90%] mx-auto mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Scroll__WEBPACK_IMPORTED_MODULE_3__.WhyAutomationDemo, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\WhyAutomation\\\\WhyAuto.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = WhyAuto;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhyAuto);\nvar _c;\n$RefreshReg$(_c, \"WhyAuto\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WhyAutomation/WhyAuto.tsx\n"));

/***/ })

});