import { NextResponse } from "next/server";
import nodemailer from "nodemailer";

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.gmail.com",
  port: Number(process.env.EMAIL_PORT) || 587,
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Email recipient (your email address)
const recipientEmail = process.env.RECIPIENT_EMAIL;

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { name, email, company, services, message } = data;

    // Basic validation
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: "Name, email, and message are required" },
        { status: 400 }
      );
    }

    // Format selected services
    const selectedServices = services
      .map((serviceId: string) => {
        const serviceMap: { [key: string]: string } = {
          "ai-agents": "AI Agents",
          consulting: "Business Consulting",
          automation: "Workflow Automation",
          integration: "System Integration",
        };
        return serviceMap[serviceId] || serviceId;
      })
      .join(", ");

    // Create email content
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: recipientEmail,
      subject: `Contact Form Submission from ${name}`,
      html: `
        <h1>Contact Form Submission</h1>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Company:</strong> ${company || "Not provided"}</p>
        <p><strong>Services Interested In:</strong> ${
          selectedServices || "None selected"
        }</p>
        <h2>Message:</h2>
        <p>${message.replace(/\n/g, "<br>")}</p>
      `,
      text: `
        Contact Form Submission
        
        Name: ${name}
        Email: ${email}
        Company: ${company || "Not provided"}
        Services Interested In: ${selectedServices || "None selected"}
        
        Message:
        ${message}
      `,
      replyTo: email,
    };

    // Send email
    await transporter.sendMail(mailOptions);

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error("Contact form submission error:", error);
    return NextResponse.json(
      { error: "Failed to process your request" },
      { status: 500 }
    );
  }
}
