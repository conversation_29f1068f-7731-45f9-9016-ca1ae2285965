import React from 'react'
import HeroTextSection from "./Text";

const HeroGradient = () => {
  return (
    <div className="min-h-screen flex items-center bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      {/* Decorative Elements - same as in Footer */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      <div className="absolute -top-24 -right-24 h-64 w-64 rounded-full bg-blue-500/10 blur-3xl"></div>
      <div className="absolute -bottom-32 -left-32 h-64 w-64 rounded-full bg-purple-500/10 blur-3xl"></div>
      
      <div className="container mx-0 md:pl-32 relative">
        <HeroTextSection />
      </div>
    </div>
  )
}

export default HeroGradient