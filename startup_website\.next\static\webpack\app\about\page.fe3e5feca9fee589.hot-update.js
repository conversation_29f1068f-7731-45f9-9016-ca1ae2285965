"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/Values/Grid.tsx":
/*!****************************************!*\
  !*** ./src/components/Values/Grid.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlowingEffectDemo: () => (/* binding */ GlowingEffectDemo),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/glowing-effect */ \"(app-pages-browser)/./src/components/ui/glowing-effect.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ GlowingEffectDemo,default auto */ \n\n\n\n\nfunction GlowingEffectDemo() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-4 xl:max-h-[34rem] xl:grid-rows-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Innovation\",\n                description: \"We push boundaries and embrace cutting-edge technologies to create solutions that transform industries.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Collaboration\",\n                description: \"We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Excellence\",\n                description: \"We commit to delivering high-quality products that exceed expectations and stand the test of time.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 15\n                }, void 0),\n                title: \"User-Centered\",\n                description: \"We design with empathy, putting users at the center of everything we create to solve real problems.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Impact\",\n                description: \"We measure success by the positive difference our solutions make in people's lives and businesses.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = GlowingEffectDemo;\nconst GridItem = (param)=>{\n    let { area, icon, title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"min-h-[14rem] list-none \".concat(area),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-full rounded-2xl border border-white/10 p-2 md:rounded-3xl md:p-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__.GlowingEffect, {\n                    spread: 40,\n                    glow: true,\n                    disabled: false,\n                    proximity: 64,\n                    inactiveZone: 0.01\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 md:p-6 bg-black/50 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1 flex-col justify-between gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-fit rounded-lg border border-gray-600 p-2\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"-tracking-4 pt-0.5 font-manrope text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-dm-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = GridItem;\nconst Grid = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__.BlurFade, {\n            direction: \"up\",\n            delay: 0.2,\n            offset: 30,\n            inViewMargin: \"-10%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlowingEffectDemo, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = Grid;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Grid);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GlowingEffectDemo\");\n$RefreshReg$(_c1, \"GridItem\");\n$RefreshReg$(_c2, \"Grid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Values/Grid.tsx\n"));

/***/ })

});