"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/Values/Grid.tsx":
/*!****************************************!*\
  !*** ./src/components/Values/Grid.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Grid = ()=>{\n    const cardData = [\n        {\n            title: \"Innovation\",\n            description: \"We push boundaries and embrace cutting-edge technologies to create solutions that transform industries.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconRocket, {\n                size: 28,\n                stroke: 1.5\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 14,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"Collaboration\",\n            description: \"We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconBrain, {\n                size: 28,\n                stroke: 1.5\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"Excellence\",\n            description: \"We commit to delivering high-quality products that exceed expectations and stand the test of time.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconDeviceAnalytics, {\n                size: 28,\n                stroke: 1.5\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 26,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"User-Centered\",\n            description: \"We design with empathy, putting users at the center of everything we create to solve real problems.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconAppWindow, {\n                size: 28,\n                stroke: 1.5\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"Adaptability\",\n            description: \"We embrace change, continuously learn, and pivot quickly to stay ahead in a rapidly evolving landscape.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconCloud, {\n                size: 28,\n                stroke: 1.5\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"Impact\",\n            description: \"We measure success by the positive difference our solutions make in people's lives and businesses.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconCode, {\n                size: 28,\n                stroke: 1.5\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n            direction: \"up\",\n            delay: 0.2,\n            offset: 30,\n            inViewMargin: \"-10%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-5 lg:gap-6 max-w-6xl mx-auto\",\n                    children: cardData.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                            direction: \"up\",\n                            delay: 0.1 * index,\n                            offset: 15,\n                            inViewMargin: \"-10%\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ValueCard, {\n                                title: card.title,\n                                description: card.description,\n                                icon: card.icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Grid;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Grid);\nvar _c;\n$RefreshReg$(_c, \"Grid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Values/Grid.tsx\n"));

/***/ })

});