"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Benefits/Benefits.tsx":
/*!**********************************************!*\
  !*** ./src/components/Benefits/Benefits.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Benefits = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pb-24 md:pb-32 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__.BlurFade, {\n            direction: \"up\",\n            delay: 0.2,\n            offset: 50,\n            inViewMargin: \"-10%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[95%] md:w-[55%] mx-auto rounded-2xl mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: \"/images/benefits.png\",\n                    alt: \"AI operational benefits illustration\",\n                    className: \"w-full h-auto rounded-xl shadow-lg\",\n                    width: 1200,\n                    height: 800,\n                    priority: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Benefits\\\\Benefits.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Benefits;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Benefits);\nvar _c;\n$RefreshReg$(_c, \"Benefits\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Benefits/Benefits.tsx\n"));

/***/ })

});