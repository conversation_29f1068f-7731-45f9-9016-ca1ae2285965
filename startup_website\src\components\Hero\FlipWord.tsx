"use client";

import React, { useState, useEffect } from "react";
import { FlipWords } from "../ui/flip-words";
import { TypewriterEffect } from "../ui/typewriter-effect";

export function FlipWord() {
  const words = ["Streamlines", "Accelerates", "Automates", "Transforms"];
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if we're running on the client side
    if (typeof window !== "undefined") {
      // Set initial state
      setIsMobile(window.innerWidth < 768);

      // Add resize listener
      const handleResize = () => {
        setIsMobile(window.innerWidth < 768);
      };

      window.addEventListener("resize", handleResize);

      // Cleanup
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  return (
    <div className="flex flex-col justify-center items-center text-center">
      <div className="text-4xl md:text-6xl lg:text-7xl font-semibold">
        <span className="text-white">Agentic AI That </span>
        <br />
        <span className="text-white">
          {isMobile ? (
            <TypewriterEffect words={words} />
          ) : (
            <FlipWords words={words} />
          )}
        </span>
      </div>
      <div className="text-4xl md:text-6xl lg:text-7xl font-semibold text-white">
        Your Business
      </div>
    </div>
  );
}
