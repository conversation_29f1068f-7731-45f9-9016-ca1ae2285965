"use client";

import React from "react";
import { <PERSON><PERSON> } from "./<PERSON><PERSON>";
import { BlurFade } from "../ui/blur-fade";

const Features: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 overflow-hidden">
      <BlurFade direction="up" delay={0.2} offset={50} inViewMargin="-10%">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            Features
          </span>
        </div>

        <div className="container mx-auto">
          <div className="mb-8 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4">
              You Stay in Control. We’ll Handle the Busywork.
            </h2>
            <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl pl-5 pr-5 leading-relaxed">
              From seamless communication to streamlined scheduling, our AI-driven tools work quietly in the background—so you can focus on leading, building, and scaling. Explore the smart systems that make it all happen effortlessly.
            </p>
          </div>
        </div>
      </BlurFade>

      <div className="w-[95%] md:w-[80%] mx-auto rounded-2xl bg-black/30 border border-white/10 mt-12">
        <Bento />
      </div>
    </section>
  );
};

export default Features;
