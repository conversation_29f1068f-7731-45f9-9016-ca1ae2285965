"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";

const Video = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(1);
  const [translateY, setTranslateY] = useState(0);
  const animationFrameRef = useRef<number | null>(null);
  const scrollRef = useRef(0);
  const isInitializedRef = useRef(false);

  // Memoize constants used in calculations
  const constants = useMemo(() => ({
    minScale: 0.4,
    totalScreens: 4,
    shrinkScreens: 2,
    moveUpScreens: 2,
    viewportHeight: 0, // Will be set in useEffect
    fixedSpacing: 0,
  }), []);

  // Initialize and handle viewport size updates
  useEffect(() => {
    const updateViewportHeight = () => {
      constants.viewportHeight = window.innerHeight;
      // Force an animation update when size changes
      if (isInitializedRef.current) {
        updateAnimation();
      }
    };

    // Debounced resize handler
    let resizeTimer: ReturnType<typeof setTimeout> | null = null;
    const handleResize = () => {
      if (resizeTimer) clearTimeout(resizeTimer);
      resizeTimer = setTimeout(updateViewportHeight, 100);
    };

    window.addEventListener('resize', handleResize);
    
    // Initial setup
    updateViewportHeight();
    isInitializedRef.current = true;
    
    // Force first animation frame
    animationFrameRef.current = requestAnimationFrame(updateAnimation);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimer) clearTimeout(resizeTimer);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [constants]);

  // Define animation update function outside the scroll handler
  const updateAnimation = () => {
    animationFrameRef.current = null;
    
    if (!containerRef.current || !isInitializedRef.current) return;

    const scrollY = scrollRef.current;
    const viewportHeight = constants.viewportHeight || window.innerHeight; // Fallback

    // Calculate progress for shrinking (first 2 screens)
    const shrinkEnd = constants.shrinkScreens * viewportHeight;
    const shrinkProgress = Math.min(1, scrollY / Math.max(1, shrinkEnd));

    // Calculate target scale based on shrink progress
    const targetScale = Math.max(
      constants.minScale,
      1 - shrinkProgress * (1 - constants.minScale)
    );

    // Apply easing to scale change for smoother effect
    const newScale = scale + (targetScale - scale) * 0.15; // Increased easing factor
    setScale(newScale);

    // Calculate and apply translateY when scale reaches minimum (next 2 screens)
    if (scrollY > shrinkEnd) {
      const moveUpStart = shrinkEnd;
      const moveUpEnd = moveUpStart + constants.moveUpScreens * viewportHeight;
      const moveUpProgress = Math.min(
        1,
        (scrollY - moveUpStart) / Math.max(1, moveUpEnd - moveUpStart)
      );

      // Increase translation distance to ensure full exit
      const maxMoveUp = -(viewportHeight + viewportHeight / constants.minScale);
      const targetTranslateY = maxMoveUp * moveUpProgress;

      // Apply easing to translateY for smoother animation
      const newTranslateY = translateY + (targetTranslateY - translateY) * 0.15;
      setTranslateY(newTranslateY);
    } else {
      // Reset translateY when in the first half of scrolling
      setTranslateY(translateY * 0.85); // Faster return to 0
    }
  };

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      // Store scroll position to use in the animation frame
      scrollRef.current = window.scrollY;

      // Only schedule animation frame if not already scheduled
      if (!animationFrameRef.current) {
        animationFrameRef.current = requestAnimationFrame(updateAnimation);
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    
    // Ensure we have initial values set
    handleScroll();
    
    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [constants, scale, translateY]);

  return (
    <div className="relative" ref={containerRef}>
      {/* Video container with fixed height */}
      <div className="h-screen w-full relative overflow-hidden">
        <div
          className="fixed top-0 left-0 w-full h-screen flex items-center justify-center will-change-transform"
          style={{
            transform: `scale(${Math.max(0.1, scale).toFixed(3)}) translateY(${translateY.toFixed(1)}px)`,
            zIndex: -1,
          }}
        >
          <video
            ref={videoRef}
            className="w-full h-full object-cover transition-all duration-300 rounded-3xl"
            autoPlay
            muted
            loop
            playsInline
            preload="auto"
          >
            <source src="/video.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>

      {/* Spacer div to make total height 4 screens */}
      <div className="h-[280vh]"></div>
    </div>
  );
};

export default Video;