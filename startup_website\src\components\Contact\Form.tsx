"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";

type FormData = {
  name: string;
  email: string;
  company: string;
  message: string;
  services: string[];
};

type ApiError = {
  message: string;
};

const services = [
  { id: "ai-agents", name: "AI Agents" },
  { id: "consulting", name: "Business Consulting" },
  { id: "automation", name: "Workflow Automation" },
  { id: "integration", name: "System Integration" },
];

const Form = () => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    company: "",
    message: "",
    services: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleServiceToggle = (serviceId: string) => {
    setFormData((prev) => {
      if (prev.services.includes(serviceId)) {
        return {
          ...prev,
          services: prev.services.filter((id) => id !== serviceId),
        };
      } else {
        return {
          ...prev,
          services: [...prev.services, serviceId],
        };
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Send data to our API endpoint
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to submit the form");
      }

      setSubmitted(true);
      // Reset form after successful submission
      setFormData({
        name: "",
        email: "",
        company: "",
        message: "",
        services: [],
      });
    } catch (err: unknown) {
      const error = err as Error | ApiError;
      setError(
        error.message || "Something went wrong. Please try again later."
      );
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      {submitted ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-b from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-white/10 rounded-2xl p-8 text-center"
        >
          <div className="mx-auto w-16 h-16 flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-white mb-2">
            Message Received!
          </h3>
          <p className="text-white/70 text-lg mb-6">
            Thank you for reaching out. We&apos;ll get back to you as soon as
            possible.
          </p>
          <button
            onClick={() => setSubmitted(false)}
            className="px-6 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 hover:bg-white/85 hover:shadow-md hover:translate-y-[-4px]"
          >
            Send Another Message
          </button>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-500/10 border border-red-500/50 text-red-500 rounded-lg p-4 text-sm">
              {error}
            </div>
          )}

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label
                htmlFor="name"
                className="block text-white text-sm font-medium"
              >
                Full Name <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full bg-black/30 border border-white/10 rounded-lg py-3 px-4 text-white placeholder:text-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                placeholder="John Doe"
              />
            </div>

            <div className="space-y-2">
              <label
                htmlFor="email"
                className="block text-white text-sm font-medium"
              >
                Email <span className="text-red-500">*</span>
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="w-full bg-black/30 border border-white/10 rounded-lg py-3 px-4 text-white placeholder:text-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label
              htmlFor="company"
              className="block text-white text-sm font-medium"
            >
              Company
            </label>
            <input
              id="company"
              name="company"
              type="text"
              value={formData.company}
              onChange={handleChange}
              className="w-full bg-black/30 border border-white/10 rounded-lg py-3 px-4 text-white placeholder:text-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
              placeholder="Acme Inc."
            />
          </div>

          <div className="space-y-3">
            <label className="block text-white text-sm font-medium">
              Services You&apos;re Interested In
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {services.map((service) => (
                <div key={service.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={service.id}
                    checked={formData.services.includes(service.id)}
                    onChange={() => handleServiceToggle(service.id)}
                    className="hidden peer"
                  />
                  <label
                    htmlFor={service.id}
                    className="w-full cursor-pointer bg-black/30 border border-white/10 rounded-lg py-2.5 px-3.5 text-sm text-white/80 transition-all hover:bg-black/50 hover:border-white/20 peer-checked:bg-blue-600/20 peer-checked:border-blue-500/50 peer-checked:text-white text-center"
                  >
                    {service.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <label
              htmlFor="message"
              className="block text-white text-sm font-medium"
            >
              Message <span className="text-red-500">*</span>
            </label>
            <textarea
              id="message"
              name="message"
              required
              rows={5}
              value={formData.message}
              onChange={handleChange}
              className="w-full bg-black/30 border border-white/10 rounded-lg py-3 px-4 text-white placeholder:text-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
              placeholder="Tell us about your project or requirements..."
            />
          </div>

          <div className="pt-2">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`w-full px-6 py-4 bg-white text-black font-medium rounded-lg transition-all duration-300 hover:bg-white/85 hover:shadow-md hover:translate-y-[-4px] ${
                isSubmitting ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-black"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Sending...
                </div>
              ) : (
                "Submit Request"
              )}
            </button>
          </div>

          <p className="text-center text-white/40 text-xs mt-6">
            By submitting this form, you agree to our{" "}
            <a href="/privacy" className="underline hover:text-white/60">
              Privacy Policy
            </a>{" "}
            and{" "}
            <a href="/terms" className="underline hover:text-white/60">
              Terms of Service
            </a>
            .
          </p>
        </form>
      )}
    </div>
  );
};

export default Form;
