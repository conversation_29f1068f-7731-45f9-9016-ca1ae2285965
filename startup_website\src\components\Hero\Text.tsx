import React from "react";
import { FlipWord } from "./FlipWord";
import Link from "next/link";
import { BlurFade } from "../ui/blur-fade";

const HeroTextSection = () => {
  return (
    <div className="flex flex-col items-center justify-center px-6 md:px-12 lg:px-16">
      <div className="text-center max-w-4xl">
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-semibold text-white leading-tight mb-8">
          <FlipWord />
        </h1>

        {/* Centered subheading with gradient text */}
        <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
        <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto mb-12 leading-relaxed">
          Harness the next generation of agentic AI designed to think, act, and adapt like an expert. Our solutions automate processes, optimize operations, and empower teams to focus on what matters most — driving growth, innovation, and sustainable success.
        </p>
        </BlurFade>

        {/* Centered Call-to-Action Buttons */}

        <div className="flex flex-col w-full sm:flex-row gap-5 items-center justify-center">
          <Link href="/about">
            <button className="md:px-6 px-4 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]">
              Learn More
            </button>
          </Link>
          <Link href="/contact">
            <button className="md:hidden px-4 py-3 bg-transparent text-white font-medium border rounded-lg transition-all duration-300 cursor-pointer hover:shadow-md hover:translate-y-[-4px]">
              Book Free Consulting
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HeroTextSection;
