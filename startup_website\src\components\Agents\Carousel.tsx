import { useState, useEffect, useRef } from "react";
import Card from "./Card";
import { motion } from "framer-motion";
import agentsData from "../../data/agents.json";

const Carousel = () => {
  const [cardsPerView, setCardsPerView] = useState(4);
  const [isHovering, setIsHovering] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  const lastMoveTimeRef = useRef<number>(Date.now());
  const animationFrameRef = useRef<number | null>(null);
  const containerWidthRef = useRef<number>(0);

  const totalCards = agentsData.length;

  // Create array for rendering - use enough duplicates to ensure continuous scrolling
  const displayAgents = [...agentsData, ...agentsData, ...agentsData];

  // Adjust cards per page based on screen size
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 640;
      setIsMobile(mobile);

      if (mobile) {
        setCardsPerView(1);
      } else if (window.innerWidth < 1024) {
        setCardsPerView(2);
      } else {
        setCardsPerView(4);
      }

      if (carouselRef.current) {
        containerWidthRef.current = carouselRef.current.offsetWidth;
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Reset currentIndex when cardsPerView changes to prevent visual jumps
  useEffect(() => {
    setCurrentIndex(0);
    lastMoveTimeRef.current = Date.now();
  }, [cardsPerView]);

  // Animation effect with different timing for mobile
  useEffect(() => {
    const animate = () => {
      const now = Date.now();
      const elapsed = now - lastMoveTimeRef.current;

      // Longer pause between slides for mobile
      const pauseDuration = isMobile ? 3500 : 2000;

      if (!isHovering && elapsed >= pauseDuration) {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % totalCards);
        lastMoveTimeRef.current = now;
      }

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isHovering, totalCards, isMobile]);

  const handleMouseEnter = () => setIsHovering(true);
  const handleMouseLeave = () => {
    setIsHovering(false);
    // Adjust reset time based on device type
    lastMoveTimeRef.current = Date.now() - (isMobile ? 3000 : 2000);
  };

  // Fixed gap size in percentage of total width to ensure consistency
  const gapPercentage = 2; // 2% gap between items

  // Calculate card width as percentage
  const cardWidth = (100 - gapPercentage * (cardsPerView - 1)) / cardsPerView;

  // Each slide should move exactly (cardWidth + gapPercentage)%
  const slideDistance = cardWidth + gapPercentage;

  // Transition duration based on device type
  const transitionDuration = isMobile ? 0.6 : 0.3;

  return (
    <div
      className="relative max-w-[90%] mx-auto pt-4 overflow-hidden"
      ref={carouselRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleMouseEnter}
      onTouchEnd={handleMouseLeave}
    >
      <div className="overflow-hidden">
        <motion.div
          className="flex"
          style={{ gap: `${gapPercentage}%` }}
          animate={{
            x: `-${currentIndex * slideDistance}%`,
          }}
          transition={{
            ease: "easeInOut",
            duration: transitionDuration,
          }}
        >
          {displayAgents.map((agent, index) => (
            <motion.div
              key={`${agent.title}-${index}`}
              className="flex-shrink-0"
              style={{
                width: `${cardWidth}%`,
              }}
            >
              <Card
                category={agent.category}
                title={agent.title}
                workflows={agent.workflows}
                image={agent.image}
              />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default Carousel;
