"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/AboutUs/AboutUs.tsx":
/*!********************************************!*\
  !*** ./src/components/AboutUs/AboutUs.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst About = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                autoPlay: true,\n                loop: true,\n                muted: true,\n                playsInline: true,\n                className: \"absolute inset-0 w-full h-full object-cover z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: \"/about-us-vid.mp4\",\n                        type: \"video/mp4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Your browser does not support the video tag.\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/50 z-10\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 text-center max-w-4xl mx-auto px-6 md:px-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_2__.BlurFade, {\n                    direction: \"up\",\n                    delay: 0.1,\n                    offset: 50,\n                    inViewMargin: \"-10%\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl font-dm-sans\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 font-manrope text-white\",\n                            children: \"Join Us on the Journey to AGI and Smarter Automation\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/[0.9] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed font-dm-sans\",\n                            children: \"We are pioneers in creating agentic AI solutions that revolutionize the way businesses operate, connect, and innovate.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\AboutUs\\\\AboutUs.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = About;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (About);\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AboutUs/AboutUs.tsx\n"));

/***/ })

});