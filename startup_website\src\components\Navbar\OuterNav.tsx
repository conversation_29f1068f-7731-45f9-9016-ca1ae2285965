"use client";

import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { InnerNav } from "./InnerNav";
import { ShimmerButton } from "../ui/shimmer-button";
import { motion, AnimatePresence } from "framer-motion";

const OuterNav = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Add scroll event listener to change navbar appearance on scroll
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <nav
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        scrolled
          ? "backdrop-blur-lg bg-transparent shadow-sm"
          : "backdrop-blur-sm bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 md:px-32">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/images/logo.png" // Replace with your logo path
              alt="Company Logo"
              width={120}
              height={40}
              className="h-16 w-auto"
            />
          </Link>
          <div className="hidden md:flex items-center space-x-8">
            <InnerNav className="text-white" />
          </div>

          {/* Book Now Button - Hidden on mobile */}
          <div className="hidden md:block">
            <Link href="/contact">
              <ShimmerButton>
                <span>Book Free Consulting</span>
              </ShimmerButton>
            </Link>
          </div>

          {/* Mobile Menu Button with Animation */}
          <div className="md:hidden flex items-center">
            <button
              className="text-white hover:text-blue-200 focus:outline-none relative w-6 h-6"
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
            >
              <div className="absolute inset-0">
                <motion.span
                  className="absolute top-2 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu"
                  animate={mobileMenuOpen ? { rotate: 45, y: 6 } : { rotate: 0, y: 0 }}
                  transition={{ duration: 0.2 }}
                />
                <motion.span
                  className="absolute top-3.5 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu"
                  animate={mobileMenuOpen ? { opacity: 0 } : { opacity: 1 }}
                  transition={{ duration: 0.2 }}
                />
                <motion.span
                  className="absolute top-5 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu"
                  animate={mobileMenuOpen ? { rotate: -45, y: -6 } : { rotate: 0, y: 0 }}
                  transition={{ duration: 0.2 }}
                />
              </div>
            </button>
          </div>
        </div>

        {/* Animated Mobile Navigation Menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div 
              className="md:hidden fixed left-0 right-0 overflow-hidden"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="bg-black/90 backdrop-blur-lg shadow-lg py-2">
                <div className="flex flex-col items-center space-y-1">
                  <Link
                    href="/agents"
                    className="w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    AI Agents
                  </Link>
                  <Link
                    href="/resources"
                    className="w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Resources
                  </Link>
                  <Link
                    href="/about"
                    className="w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    About
                  </Link>
                  <Link
                    href="/contact"
                    className="w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Contact Us
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  );
};

export default OuterNav;