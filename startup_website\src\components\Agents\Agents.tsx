"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import Carousel from "./Carousel";
import { BlurFade } from "../ui/blur-fade";

const Agents: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 overflow-hidden">
      <BlurFade direction="up" delay={0.2} offset={50} inViewMargin="-10%">
      <div className="text-center mb-6">
        <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
          AI Agents
        </span>
      </div>

      <div className="container mx-auto">
        <div className="mb-8 max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4">
            AI Agents Built for Every Workflow
          </h2>
          <p className="mb-8 text-white/[0.7] text-lg md:text-xl px-5 md:px-0 leading-relaxed">
            Our AI agents integrate effortlessly into your existing systems, handling everything from routine tasks to complex processes. Experience a seamless, intelligent transformation that empowers your business to work smarter, faster, and more effectively than ever before.
          </p>
          <Link href="/agents">
            <button className="px-6 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]">
              Explore All Agents
            </button>
          </Link>
        </div>
      </div>
      </BlurFade>
      <Carousel />

      {/* Flowchart Section */}
      <BlurFade direction="up" delay={0.4} offset={50} inViewMargin="-10%">
        <div className="mt-16 md:mt-24">
          <div className="text-center mb-8 md:mb-12">
            <h3 className="text-3xl md:text-4xl lg:text-5xl font-semibold mb-4 px-4">
              How Our AI Agents Work
            </h3>
            <p className="text-white/[0.7] text-lg md:text-xl max-w-3xl mx-auto px-5 md:px-0 leading-relaxed">
              Discover the seamless workflow that powers our intelligent automation system
            </p>
          </div>

          {/* Fullscreen Flowchart Container */}
          <div className="relative w-full">
            {/* Desktop/Tablet View */}
            <div className="hidden md:block relative w-full h-[70vh] lg:h-[80vh] rounded-2xl overflow-hidden bg-gradient-to-br from-slate-900/50 to-slate-800/30 backdrop-blur-sm border border-white/10">
              <Image
                src="/images/flowchart.png"
                alt="AI Agents Workflow Flowchart"
                fill
                className="object-contain p-6 lg:p-8"
                style={{ objectFit: 'contain' }}
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
            </div>

            {/* Mobile View */}
            <div className="block md:hidden relative w-full">
              <div className="relative aspect-[3/4] w-full rounded-xl overflow-hidden bg-gradient-to-br from-slate-900/50 to-slate-800/30 backdrop-blur-sm border border-white/10">
                <Image
                  src="/images/flowchart.png"
                  alt="AI Agents Workflow Flowchart"
                  fill
                  className="object-contain p-4"
                  style={{ objectFit: 'contain' }}
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>
      </BlurFade>
    </section>
  );
};

export default Agents;
