"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import Carousel from "./Carousel";
import { BlurFade } from "../ui/blur-fade";

const Agents: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 overflow-hidden">
      <BlurFade direction="up" delay={0.2} offset={50} inViewMargin="-10%">
      <div className="text-center mb-6">
        <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
          AI Agents
        </span>
      </div>

      <div className="container mx-auto">
        <div className="mb-8 max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4">
            AI Agents Built for Every Workflow
          </h2>
          <p className="mb-8 text-white/[0.7] text-lg md:text-xl px-5 md:px-0 leading-relaxed">
            Our AI agents integrate effortlessly into your existing systems, handling everything from routine tasks to complex processes. Experience a seamless, intelligent transformation that empowers your business to work smarter, faster, and more effectively than ever before.
          </p>
          <Link href="/agents">
            <button className="px-6 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]">
              Explore All Agents
            </button>
          </Link>
        </div>
      </div>
      </BlurFade>
      <Carousel />

      {/* Fullscreen Flowchart */}
      <div className="mt-16 md:mt-24 w-full relative">
        <Image
          src="/images/flowchart.png"
          alt="AI Agents Workflow Flowchart"
          width={1200} // Use actual image dimensions
          height={800} // Use actual image dimensions
          className="w-full h-auto"
          priority
        />
      </div>
    </section>
  );
};

export default Agents;
