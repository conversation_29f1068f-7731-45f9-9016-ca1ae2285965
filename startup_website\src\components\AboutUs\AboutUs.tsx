"use client";

import React from "react";
import { BlurFade } from "../ui/blur-fade";

const About: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Video */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover z-0"
      >
        <source src="/about-us-vid.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>

      {/* Content */}
      <div className="relative z-20 text-center max-w-4xl mx-auto px-6 md:px-12">
        <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
          <div className="mb-8">
            <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl font-dm-sans">
              About Us
            </span>
          </div>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 font-manrope text-white">
            Join Us on the Journey to AGI and Smarter Automation
          </h2>

          <p className="text-white/[0.9] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed font-dm-sans">
            We are pioneers in creating agentic AI solutions that revolutionize the way businesses operate, connect, and innovate.
          </p>
        </BlurFade>
      </div>
    </section>
  );
};

export default About;
