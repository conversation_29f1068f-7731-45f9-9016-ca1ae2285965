"use client";

import React from "react";
import Image from "next/image";
import { BlurFade } from "../ui/blur-fade";

const About: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 mt-32 md:mt-0 overflow-hidden">
      <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            About Us
          </span>
        </div>

        <div className="container mx-auto">
          <div className="mb-8 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 px-4 md:px-0">
              On a joint path towards AGI
            </h2>
            <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed px-5 md:px-0">
              We&apos;re building generative AI agents to help organizations
              automate repetitive manual tasks, boost productivity, and enable
              teams to focus on the work that really matters.
            </p>
          </div>
        </div>
      </BlurFade>

      <div className="container mx-auto mt-12 flex justify-center align-center">
        <div className="md:max-w-[70%] px-3 md:px-0 rounded-xl overflow-hidden">
          <Image
            src="/images/aboutus.jpg"
            alt="Our team working on AI solutions"
            width={1200}
            height={600}
            className="object-cover"
            priority
          />
        </div>
      </div>
    </section>
  );
};

export default About;
