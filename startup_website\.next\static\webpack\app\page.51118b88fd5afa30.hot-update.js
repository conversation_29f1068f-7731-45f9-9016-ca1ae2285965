"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Hero/Text.tsx":
/*!**************************************!*\
  !*** ./src/components/Hero/Text.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _FlipWord__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FlipWord */ \"(app-pages-browser)/./src/components/Hero/FlipWord.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n\n\n\n\n\nconst HeroTextSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center px-6 md:px-12 lg:px-16 py-12 md:py-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl md:text-6xl lg:text-7xl font-semibold text-white leading-tight mb-6 md:mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlipWord__WEBPACK_IMPORTED_MODULE_2__.FlipWord, {}, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_4__.BlurFade, {\n                    direction: \"up\",\n                    delay: 0.1,\n                    offset: 50,\n                    inViewMargin: \"-10%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto mb-8 md:mb-12 leading-relaxed\",\n                        children: \"Harness the next generation of agentic AI designed to think, act, and adapt like an expert. Our solutions automate processes, optimize operations, and empower teams to focus on what matters most — driving growth, innovation, and sustainable success.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-full sm:flex-row gap-4 md:gap-5 items-center justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/about\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full sm:w-auto md:px-6 px-4 py-3 bg-white text-black font-medium rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                children: \"Learn More\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/contact\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full sm:w-auto md:hidden px-4 py-3 bg-transparent text-white font-medium border border-white/30 rounded-lg transition-all duration-300 cursor-pointer hover:bg-white/10 hover:border-white/50 hover:shadow-md hover:translate-y-[-4px]\",\n                                children: \"Book Free Consulting\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Hero\\\\Text.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeroTextSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroTextSection);\nvar _c;\n$RefreshReg$(_c, \"HeroTextSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Hero/Text.tsx\n"));

/***/ })

});