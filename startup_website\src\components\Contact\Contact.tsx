"use client";

import React from "react";
import Form from "./Form";
import { BlurFade } from "../ui/blur-fade";

const Contact: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 pt-32 overflow-hidden">
      <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
      <div className="text-center mb-6">
        <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
          Contact Us
        </span>
      </div>

      <div className="container mx-auto">
        <div className="mb-12 max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6">
            Let us explore what we can do for you
          </h2>
          <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed">
            Tell us a little about yourself in the form below and we will contact you to discuss how we can help you achieve your goals.
          </p>
        </div>
      </div>
      </BlurFade>

      <Form />
    </section>
  );
};

export default Contact;
