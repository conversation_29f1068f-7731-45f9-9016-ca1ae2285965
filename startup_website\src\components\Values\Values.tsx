"use client";

import React from "react";
import Grid from "./Grid";
import { BlurFade } from "../ui/blur-fade";

const Values: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 overflow-hidden">
      <BlurFade direction="up" delay={0.1} offset={50} inViewMargin="-10%">
      <div className="text-center mb-6">
        <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
          Our Values
        </span>
      </div>

      <div className="container mx-auto mb-8">
        <div className="mb-8 max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 px-4 md:px-0">
            Building great big things starts with a decision, thought, or even a click
          </h2>
          <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl mx-auto leading-relaxed px-5 md:px-0">
            That is what inspires us, the potential of the butterfly effect leading to the creation of something tangible and meaningful. So we’ve vowed to give all the movers, shakers, builders, and makers, a platform to do more, with less. 
          </p>
        </div>
      </div>
      </BlurFade>

      <div>
        <Grid />
      </div>
    </section>
  );
};

export default Values;
