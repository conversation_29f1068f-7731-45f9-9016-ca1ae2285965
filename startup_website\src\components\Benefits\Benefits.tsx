"use client";

import React from "react";
import Image from "next/image";
import { BlurFade } from "../ui/blur-fade";

const Benefits: React.FC = () => {
  return (
    <section className="relative pb-24 md:pb-32 overflow-hidden">
      {/* <BlurFade direction="up" delay={0.2} offset={50} inViewMargin="-10%">
        <div className="text-center mb-6">
          <span className="inline-block text-md md:text-lg px-4 py-1 border border-white/[0.2] rounded-xl">
            Benefits
          </span>
        </div>

        <div className="container mx-auto">
          <div className="mb-8 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-6 pl-4 pr-4">
              Improve Operational Efficency and Create Leverage. Fast.
            </h2>
            <p className="text-white/[0.7] text-lg md:text-xl max-w-2xl pl-5 pr-5 leading-relaxed">
              Deploy AI agents to streamline operations and amplify your
              business&apos;s efficiency. These agents optimize processes,
              reduce delays, and enhance output, ensuring you gain a competitive
              edge with speed and precision.
            </p>
          </div>
        </div>
      </BlurFade> */}

      <div className="w-[95%] md:w-[55%] mx-auto rounded-2xl mt-12">
        <Image
          src="/images/benefits.png"
          alt="AI operational benefits illustration"
          className="w-full h-auto rounded-xl shadow-lg"
          width={1200}
          height={800}
          priority={false}
        />
      </div>
    </section>
  );
};

export default Benefits;
