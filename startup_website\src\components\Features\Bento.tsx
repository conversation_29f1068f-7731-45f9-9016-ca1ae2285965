import { CalendarIcon, FileTextIcon } from "@radix-ui/react-icons";
import { BellIcon, Share2Icon } from "lucide-react";

import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { AnimatedBeamMultipleOutputDemo } from "./AnimatedBeamMultipleOutput";
import { AnimatedListDemo } from "./AnimatedList";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import { Marquee } from "@/components/ui/marquee";

const files = [
  {
    name: "q2.pdf",
    body: "The Q2 report outlines revenue growth, operating expenses, and customer acquisition metrics across all departments. Includes performance benchmarks and projections for Q3.",
  },
  {
    name: "guide.docx",
    body: "This document provides a step-by-step onboarding process for new B2B clients, including contract workflows, data access procedures, and support escalation paths.",
  },
  {
    name: "notes.txt",
    body: "Notes from the leadership sync on April 18th. Key takeaways: increase in lead conversion, upcoming product launch timeline, and strategy for customer retention improvements.",
  },
  {
    name: "ads.xlsx",
    body: "Contains campaign performance metrics such as click-through rates, conversion rates, audience segments, and budget allocation for Q1 and Q2 initiatives.",
  },
  {
    name: "deal.pdf",
    body: "This agreement outlines terms of collaboration between Company A and Company B, including revenue sharing, exclusivity clauses, and quarterly performance reviews.",
  },
];



const features = [
  {
    Icon: FileTextIcon,
    name: "Your Files, Now Working for You",
    description: "Our AI agents don’t just read files—they understand them, gaining real-time context to make smarter decisions and deliver relevant insights.",
    href: "#",
    cta: "Learn more",
    className: "col-span-3 lg:col-span-1 bg-zinc-900 text-white",
    background: (
      <Marquee
        pauseOnHover
        className="absolute top-10 [--duration:20s] [mask-image:linear-gradient(to_top,transparent_40%,#000_100%)] dark"
      >
        {files.map((f, idx) => (
          <figure
            key={idx}
            className={cn(
              "relative w-32 cursor-pointer overflow-hidden rounded-xl border p-4",
              "border-gray-700 bg-zinc-800 hover:bg-zinc-700",
              "transform-gpu blur-[1px] transition-all duration-300 ease-out hover:blur-none"
            )}
          >
            <div className="flex flex-row items-center gap-2">
              <div className="flex flex-col">
                <figcaption className="text-sm font-medium text-white">
                  {f.name}
                </figcaption>
              </div>
            </div>
            <blockquote className="mt-2 text-xs text-gray-300">
              {f.body}
            </blockquote>
          </figure>
        ))}
      </Marquee>
    ),
  },
  {
    Icon: BellIcon,
    name: "Notifications",
    description: "Smart notification systems keep your team in the loop without the noise. Receive context-aware updates that are timely, relevant, and designed to support fast, focused action.",
    href: "#",
    cta: "Learn more",
    className: "col-span-3 lg:col-span-2 bg-zinc-900 text-white",
    background: (
      <AnimatedListDemo className="absolute right-2 top-4 h-[300px] w-full scale-75 border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_10%,#000_100%)] group-hover:scale-90 dark" />
    ),
  },
  {
    Icon: Share2Icon,
    name: "Connect Everything. Disrupt Nothing.",
    description: "Plug our AI into the tools you already use. With deep, seamless integrations across your ecosystem, automation becomes an invisible force multiplying your productivity—no rework required.",
    href: "#",
    cta: "Learn more",
    className: "col-span-3 lg:col-span-2 bg-zinc-900 text-white",
    background: (
      <AnimatedBeamMultipleOutputDemo className="absolute right-2 top-4 h-[300px] border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_10%,#000_100%)] group-hover:scale-105 dark" />
    ),
  },
  {
    Icon: CalendarIcon,
    name: "Master Your Time, Effortlessly",
    description: "Our AI keeps your schedule in sync and on track—anticipating conflicts, prioritizing tasks, and making sure you never miss a critical deadline or opportunity.",
    className: "col-span-3 lg:col-span-1 bg-zinc-900 text-white",
    href: "#",
    cta: "Learn more",
    background: (
      <Calendar
        mode="single"
        selected={new Date(2022, 4, 11, 0, 0, 0)}
        className="absolute right-0 top-10 origin-top scale-75 rounded-md border border-gray-700 bg-zinc-800 text-white transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_40%,#000_100%)] group-hover:scale-90"
      />
    ),
  },
];

export function Bento() {
  return (
    <div className="bg-zinc-950 p-6 rounded-xl">
      <BentoGrid className="bg-zinc-950">
        {features.map((feature, idx) => (
          <BentoCard key={idx} {...feature} />
        ))}
      </BentoGrid>
    </div>
  );
}
