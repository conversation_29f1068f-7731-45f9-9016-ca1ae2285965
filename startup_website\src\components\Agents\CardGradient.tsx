import Image from "next/image";
import { useState } from "react";

interface CardProps {
  category: string;
  title: string;
  workflows: string[];
  image: string;
}

const Card = ({ category, title, workflows, image }: CardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  // Generate a gradient based on category
  const getCategoryColor = (category: string) => {
    const categories: Record<string, string> = {
      "Data Extraction": "from-blue-500 to-cyan-400",
      "Customer Service": "from-green-500 to-emerald-400",
      "Financial Services & Banking": "from-purple-500 to-violet-400",
      Healthcare: "from-red-500 to-rose-400",
      "E-commerce": "from-amber-500 to-yellow-400",
      Legal: "from-indigo-500 to-blue-400",
      "Human Resources": "from-pink-500 to-rose-400",
      Marketing: "from-orange-500 to-amber-400",
      Support: "from-cyan-500 to-blue-400",
      Insurance: "from-emerald-500 to-green-400",
      Education: "from-violet-500 to-purple-400",
    };

    return categories[category] || "from-gray-500 to-slate-400";
  };

  const categoryColors = getCategoryColor(category);

  // Limit workflows to 3 maximum
  const displayWorkflows = workflows.slice(0, 3);
  const hasMoreWorkflows = workflows.length > 3;

  return (
    <div
      className="bg-gradient-to-b from-gray-900 to-black rounded-2xl overflow-hidden shadow-lg h-[400px] text-white transition-all duration-300 hover:shadow-2xl hover:shadow-blue-900/20 flex flex-col"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        transform: isHovered ? "translateY(-8px)" : "translateY(0)",
      }}
    >
      <div className="relative h-40 w-full overflow-hidden flex-shrink-0">
        <Image
          src={`/${image}`}
          alt={title}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
          className={`object-cover transition-transform duration-700 ${
            isHovered ? "scale-110" : "scale-100"
          }`}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
        <div className="absolute top-3 left-3">
          <span
            className={`text-xs bg-gradient-to-r ${categoryColors} px-3 py-1 rounded-full font-semibold uppercase tracking-wide shadow-lg`}
          >
            {category}
          </span>
        </div>
      </div>

      <div className="p-5 flex-grow flex flex-col">
        <h2 className="text-xl font-bold leading-tight mb-4 line-clamp-2">
          {title}
        </h2>
        <div className="space-y-3 flex-grow">
          <p className="text-xs font-semibold text-blue-400 uppercase tracking-wider">
            Workflows
          </p>
          <ul className="space-y-2.5">
            {displayWorkflows.map((workflow, idx) => (
              <li
                key={idx}
                className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg px-3 py-1.5 text-sm transition-colors hover:bg-gray-700/50"
              >
                <span className="line-clamp-1">{workflow}</span>
              </li>
            ))}
            {hasMoreWorkflows && (
              <li className="text-xs text-gray-400 pl-2 italic mt-1.5">
                +{workflows.length - 3} more workflows
              </li>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Card;
