"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/Values/Grid.tsx":
/*!****************************************!*\
  !*** ./src/components/Values/Grid.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlowingEffectDemo: () => (/* binding */ GlowingEffectDemo),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Rocket,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/glowing-effect */ \"(app-pages-browser)/./src/components/ui/glowing-effect.tsx\");\n/* harmony import */ var _ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/blur-fade */ \"(app-pages-browser)/./src/components/ui/blur-fade.tsx\");\n/* __next_internal_client_entry_do_not_use__ GlowingEffectDemo,default auto */ \n\n\n\n\nfunction GlowingEffectDemo() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-4 xl:max-h-[34rem] xl:grid-rows-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Innovation\",\n                description: \"We push boundaries and embrace cutting-edge technologies to create solutions that transform industries.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Collaboration\",\n                description: \"We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Excellence\",\n                description: \"We commit to delivering high-quality products that exceed expectations and stand the test of time.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 15\n                }, void 0),\n                title: \"User-Centered\",\n                description: \"We design with empathy, putting users at the center of everything we create to solve real problems.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridItem, {\n                area: \"md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Rocket_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-black dark:text-neutral-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 15\n                }, void 0),\n                title: \"Impact\",\n                description: \"We measure success by the positive difference our solutions make in people's lives and businesses.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = GlowingEffectDemo;\nconst GridItem = (param)=>{\n    let { area, icon, title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"min-h-[14rem] list-none \".concat(area),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-full rounded-2xl border border-white/10 p-2 md:rounded-3xl md:p-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_glowing_effect__WEBPACK_IMPORTED_MODULE_2__.GlowingEffect, {\n                    spread: 40,\n                    glow: true,\n                    disabled: false,\n                    proximity: 64,\n                    inactiveZone: 0.01\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 md:p-6 bg-black/50 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1 flex-col justify-between gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-fit rounded-lg border border-gray-600 p-2\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"-tracking-4 pt-0.5 font-manrope text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-dm-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = GridItem;\nconst Grid = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_blur_fade__WEBPACK_IMPORTED_MODULE_3__.BlurFade, {\n            direction: \"up\",\n            delay: 0.2,\n            offset: 30,\n            inViewMargin: \"-10%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlowingEffectDemo, {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Values\\\\Grid.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = Grid;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Grid);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GlowingEffectDemo\");\n$RefreshReg$(_c1, \"GridItem\");\n$RefreshReg$(_c2, \"Grid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Values/Grid.tsx\n"));

/***/ })

});