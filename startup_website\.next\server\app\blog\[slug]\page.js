/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/blog/[slug]/page";
exports.ids = ["app/blog/[slug]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/blog/[slug]/page.tsx */ \"(rsc)/./src/app/blog/[slug]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'blog',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/blog/[slug]/page\",\n        pathname: \"/blog/[slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTdGFydHVwJTVDJTVDV2Vic2l0ZSU1QyU1Q3N0YXJ0dXBfd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU3RhcnR1cCU1QyU1Q1dlYnNpdGUlNUMlNUNzdGFydHVwX3dlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQXdLO0FBQ3hLO0FBQ0Esc05BQWdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcU3RhcnR1cFxcXFxXZWJzaXRlXFxcXHN0YXJ0dXBfd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFN0YXJ0dXBcXFxcV2Vic2l0ZVxcXFxzdGFydHVwX3dlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcaW1hZ2UtY29tcG9uZW50LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-manrope%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22DM_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-dm-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22dmSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-manrope%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22DM_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-dm-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22dmSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer/Footer.tsx */ \"(rsc)/./src/components/Footer/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar/OuterNav.tsx */ \"(rsc)/./src/components/Navbar/OuterNav.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-manrope%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22DM_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-dm-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22dmSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/blog/[slug]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/blog/[slug]/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPost),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _data_blogPosts_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/blogPosts.json */ \"(rsc)/./src/data/blogPosts.json\");\n\n\n\n\n\n\n// Generate metadata for the page\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    const post = _data_blogPosts_json__WEBPACK_IMPORTED_MODULE_5__.find((post)=>post.slug === slug);\n    if (!post) {\n        return {\n            title: \"Post Not Found\"\n        };\n    }\n    return {\n        title: `${post.title} | Your Company Blog`,\n        description: post.excerpt\n    };\n}\nasync function BlogPost({ params }) {\n    const { slug } = await params;\n    const post = _data_blogPosts_json__WEBPACK_IMPORTED_MODULE_5__.find((post)=>post.slug === slug);\n    if (!post) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-black text-white pt-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-3xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mb-8 text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        className: \"text-white/60 hover:text-white\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60\",\n                                        children: \"/\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/blog\",\n                                        className: \"text-white/60 hover:text-white\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60\",\n                                        children: \"/\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"text-white/80 truncate max-w-[200px]\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-white/10 text-white rounded-lg text-sm\",\n                                        children: post.category\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: post.date\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: post.readTime\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl lg:text-5xl font-semibold mb-6\",\n                                children: post.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80\",\n                                children: post.excerpt\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[300px] md:h-[400px] w-full rounded-xl overflow-hidden mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: post.image,\n                            alt: post.title,\n                            fill: true,\n                            className: \"object-cover\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        className: \"prose prose-lg prose-invert max-w-none\",\n                        dangerouslySetInnerHTML: {\n                            __html: post.content\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 pt-8 border-t border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-start md:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 md:mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Share this article\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-2 bg-white/10 hover:bg-blue-900/50 rounded-full transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-2 bg-white/10 hover:bg-blue-800/50 rounded-full transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-2 bg-white/10 hover:bg-blue-700/50 rounded-full transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/blog\",\n                                    className: \"inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Back to all articles\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n// Generate static paths for all blog posts\nasync function generateStaticParams() {\n    return _data_blogPosts_json__WEBPACK_IMPORTED_MODULE_5__.map((post)=>({\n            slug: post.slug\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/blog/[slug]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"376131a79624\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcU3RhcnR1cFxcV2Vic2l0ZVxcc3RhcnR1cF93ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNzYxMzFhNzk2MjRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variable_font_manrope_weight_300_400_500_600_700_800_variableName_manrope___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-manrope\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"]}],\"variableName\":\"manrope\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Manrope\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-manrope\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"]}],\\\"variableName\\\":\\\"manrope\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variable_font_manrope_weight_300_400_500_600_700_800_variableName_manrope___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variable_font_manrope_weight_300_400_500_600_700_800_variableName_manrope___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_DM_Sans_arguments_subsets_latin_variable_font_dm_sans_weight_300_400_500_600_700_variableName_dmSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"DM_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-dm-sans\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"dmSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"DM_Sans\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-dm-sans\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"dmSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_DM_Sans_arguments_subsets_latin_variable_font_dm_sans_weight_300_400_500_600_700_variableName_dmSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_DM_Sans_arguments_subsets_latin_variable_font_dm_sans_weight_300_400_500_600_700_variableName_dmSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navbar_OuterNav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar/OuterNav */ \"(rsc)/./src/components/Navbar/OuterNav.tsx\");\n/* harmony import */ var _components_Footer_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer/Footer */ \"(rsc)/./src/components/Footer/Footer.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variable_font_manrope_weight_300_400_500_600_700_800_variableName_manrope___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_DM_Sans_arguments_subsets_latin_variable_font_dm_sans_weight_300_400_500_600_700_variableName_dmSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased bg-black text-white`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar_OuterNav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Footer/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Startup\\Website\\startup_website\\src\\components\\Footer\\Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Navbar/OuterNav.tsx":
/*!********************************************!*\
  !*** ./src/components/Navbar/OuterNav.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Startup\\Website\\startup_website\\src\\components\\Navbar\\OuterNav.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/data/blogPosts.json":
/*!*********************************!*\
  !*** ./src/data/blogPosts.json ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"id":"1","title":"How AI Agents Are Transforming Enterprise Operations in 2025","excerpt":"Explore how AI agents are revolutionizing enterprise operations in 2025, from enhanced productivity to seamless integration across industries.","content":"<h1>How AI Agents Are Transforming Enterprise Operations in 2025</h1>\\n<p>Hey there, tech enthusiasts! If you thought AI was already shaking things up, 2025 is taking it to a whole new level with AI agents leading the charge in enterprise settings. These smart systems are no longer just answering queries—they’re running the show, streamlining workflows, and making businesses more efficient than ever. Let’s dive into what’s driving this transformation.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Exploration and Development</h2>\\n<p>Research from IBM suggests that 99% of 1,000 developers working on enterprise AI applications are either exploring or actively building AI agents. That’s a massive vote of confidence in their potential to redefine business operations!</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Salesforce’s Agentforce Platform</h2>\\n<p>Salesforce is making waves with its <a href=\\"https://www.salesforce.com/products/agentforce/overview/\\">Agentforce platform</a>, which lets businesses integrate AI agents seamlessly into their existing systems. Think of it as a plug-and-play solution that boosts productivity and customer engagement without the tech headaches.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Model Improvements</h2>\\n<p>IBM highlights four game-changing advancements in AI models:\\n<ul class=\\"list-disc pl-6 my-4 space-y-2\\">\\n<li>Smaller, faster, and more efficient models.</li>\\n<li>Chain-of-Thought (COT) training for better reasoning.</li>\\n<li>Larger context windows to tackle complex tasks.</li>\\n<li>Function calling to interact with external systems smoothly.</li>\\n</ul>\\nThese upgrades mean AI agents can handle more sophisticated tasks with less computing power.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">AI Orchestrators</h2>\\n<p>Picture AI orchestrators as the conductors of an AI symphony, coordinating multiple agents to optimize workflows. They handle multilingual and multimedia data, but they need robust compliance frameworks to keep everything in check.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Human Augmentation</h2>\\n<p>Don’t worry—AI agents aren’t here to steal jobs. They’re designed to take on repetitive tasks, freeing up humans for creative and strategic work. Governance frameworks ensure these collaborations are fair and transparent.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Open Source Opportunities</h2>\\n<p>Open source AI models are creating a marketplace for AI agents, enabling monetization and supporting low-bandwidth scenarios, especially in regions like the Global South. This democratization is a big deal!</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Governance and Strategy</h2>\\n<p>To make AI agents work, businesses need solid governance to monitor performance and a strategy focused on leveraging proprietary data for maximum ROI. It’s all about balancing innovation with accountability.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">IBM’s Role</h2>\\n<p>IBM is leading the charge with solutions like <a href=\\"https://www.ibm.com/products/watsonx-orchestrate\\">watsonx Orchestrate</a> for workflow streamlining and <a href=\\"https://www.ibm.com/consulting/artificial-intelligence\\">AI consulting services</a> to guide businesses through this transformation.</p>\\n\\n<p>In 2025, AI agents are redefining how enterprises operate, making them more adaptive and competitive. It’s an exciting time, and we’re just getting started!</p>","image":"/images/blog/1.jpg","category":"ARTICLE","date":"May 30, 2025","slug":"ai-agents-enterprise-2025","readTime":"5 min read"},{"id":"2","title":"Lessons from Builder.ai: The Pitfalls of Overhyped Low-Code Platforms","excerpt":"Learn from Builder.ai’s downfall and understand the risks of overhyped low-code platforms in 2025.","content":"<h1>Lessons from Builder.ai: The Pitfalls of Overhyped Low-Code Platforms</h1>\\n<p>Hello, tech fans! The story of Builder.ai’s rise and fall is a wild ride that’s got everyone talking in 2025. Once a darling of the low-code world, its collapse offers some hard-earned lessons for anyone eyeing these platforms. Let’s break it down.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">The Rise and Fall of Builder.ai</h2>\\n<p>Builder.ai promised to make app development a breeze with its low-code platform, backed by a whopping $450 million in funding. But in 2025, it hit a wall, going into administration after burning through cash at a peak rate of $40 million per quarter, later cut to $21 million. Oh, and they owed nearly $88 million to AWS, as reported by <a href=\\"https://techcrunch.com/2025/05/20/once-worth-over-1b-microsoft-backed-builder-ai-is-running-out-of-money/\\">TechCrunch</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Behind-the-Scenes Reality</h2>\\n<p>Here’s the kicker: despite its AI-powered claims, Builder.ai leaned heavily on human engineers, according to <a href=\\"https://www.financialexpress.com/business/start-ups/why-did-microsoft-backed-1-3bn-builderai-collapse-accused-of-using-indian-codersforaiwork/3854944/\\">Financial Express</a>. This gap between marketing hype and reality was a major red flag.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">What This Means for Low-Code</h2>\\n<p>Builder.ai’s downfall shines a light on some serious risks of low-code platforms:\\n<ul class=\\"list-disc pl-6 my-4 space-y-2\\">\\n<li><strong>Vendor Lock-in</strong>: You’re tied to the platform, which can limit your options.</li>\\n<li><strong>Lack of Code Ownership</strong>: No direct control over your code can be a dealbreaker.</li>\\n<li><strong>Lack of Differentiation</strong>: Apps built on these platforms often look and feel the same.</li>\\n</ul>\\n</p>\\n\\n<p>Compared to platforms like Wix or Shopify, Builder.ai’s story shows that low-code can be handy, but truly standout digital products need real developers, creativity, and a clear vision.</p>\\n\\n<p>So, what’s the takeaway? Low-code platforms can be a great starting point, but don’t fall for the hype. Do your homework, weigh the trade-offs, and build on a foundation that’s as solid as your ambitions.</p>","image":"/images/blog/2.jpg","category":"ARTICLE","date":"May 28, 2025","slug":"builder-ai-lessons-low-code","readTime":"5 min read"},{"id":"3","title":"The Evolution of Conversational AI: What’s New in 2025","excerpt":"Discover the latest advancements in conversational AI for 2025, from hyper-personalized experiences to emotionally intelligent chatbots.","content":"<h1>The Evolution of Conversational AI: What’s New in 2025</h1>\\n<p>Hey folks, conversational AI is stealing the spotlight in 2025, and it’s not just about chatbots anymore! From smarter searches to emotionally savvy bots, let’s check out the trends making waves this year.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Conversational Co-pilots</h2>\\n<p>AI systems like Ada AI Agent and <a href=\\"https://www.microsoft.com/en-us/worklab/work-trend-index/copilots-earliest-users-teach-us-about-generative-ai-at-work/\\">Microsoft 365 Copilot</a> are boosting productivity like never before. IntellAssistant, for example, slashed development time from six months to one!</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Smarter Search</h2>\\n<p>Conversational AI is revolutionizing search, with platforms like Walmart’s iPhone app understanding user intent for spot-on results, as noted by <a href=\\"https://tech.walmart.com/content/walmart-global-tech/en_us/news/articles/walmarts-generative-ai-search-puts-more-time-back-in-customers-hands.html\\">Walmart</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Hyper-personalized Experiences</h2>\\n<p>AI is getting personal, analyzing user data to tailor responses. Bank of America’s Erica has racked up 1.5 billion interactions, offering customized financial advice, per <a href=\\"https://www.americanbanker.com/news/bank-of-america-gives-erica-a-search-bar-look-as-it-tests-gen-ai\\">American Banker</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Proactive AI</h2>\\n<p>These systems are now predicting your needs, suggesting promotions, and offering discounts based on your behavior, making interactions feel almost psychic.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Voice Assistants for All</h2>\\n<p>Multilingual voice assistants like Alexa and Siri are breaking language barriers with advanced natural language understanding, as explored by <a href=\\"https://intellias.com/from-touch-to-sound-how-voice-technology-is-changing-the-iot-landscape/\\">Intellias</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Industry-Specific Models</h2>\\n<p>Customized AI models for finance and healthcare are popping up, offering cost-effective, precise solutions for specialized tasks.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">The New Customer Service Standard</h2>\\n<p>Conversational AI is now the go-to for customer service, replacing clunky rule-based bots with smarter, more responsive systems.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Employee Evaluation Tools</h2>\\n<p>Tools like Amazon Connect’s Manager Assist are using AI to rate agent performance, streamlining training and management.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Multimodal AI</h2>\\n<p>AI that processes images, video, and audio is here, with Google Lens leading the way by combining written and visual queries, as seen on <a href=\\"https://www.zdnet.com/article/googles-new-ai-search-features-are-game-changing-for-mobile-users-ios-included/\\">ZDNet</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">AR/VR Integration</h2>\\n<p>Natural language interfaces are making augmented and virtual reality experiences more immersive.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Emotionally Intelligent Chatbots</h2>\\n<p>Chatbots are getting a heart, detecting emotions and responding with empathy to boost customer satisfaction.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Omnichannel AI</h2>\\n<p>Seamless integration across email, chat, SMS, and social media ensures a consistent experience, no matter how you connect.</p>\\n\\n<p>With the market expected to grow from $9.9 billion in 2023 to over $57 billion by 2032, conversational AI is becoming a cornerstone of our digital lives. Stay tuned for more!</p>","image":"/images/blog/3.jpg","category":"ARTICLE","date":"May 26, 2025","slug":"conversational-ai-2025","readTime":"5 min read"},{"id":"4","title":"Navigating the New AI Regulatory Landscape in 2025","excerpt":"Understand the key points of the AI Act and how it’s shaping AI development in 2025.","content":"<h1>Navigating the New AI Regulatory Landscape in 2025</h1>\\n<p>Hi everyone! As AI takes over more of our world, 2025 is bringing some serious rules to keep it in check. The EU’s AI Act is leading the charge, and it’s a big deal for anyone building or using AI. Let’s unpack what this means.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">The AI Act: A Game-Changer</h2>\\n<p>The <a href=\\"https://digital-strategy.ec.europa.eu/en/policies/regulatory-framework-ai\\">AI Act</a> (Regulation (EU) 2024/1689) is the world’s first comprehensive AI law, aiming to make AI safe, ethical, and human-centric. It’s setting a global standard, and businesses are taking notice.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Risk-Based Approach</h2>\\n<p>The Act sorts AI systems into four risk levels:\\n<ul class=\\"list-disc pl-6 my-4 space-y-2\\">\\n<li><strong>Unacceptable Risk</strong>: Bans things like manipulative AI or social scoring.</li>\\n<li><strong>High-Risk AI</strong>: Covers critical areas like infrastructure and employment, requiring strict measures like risk assessments and human oversight.</li>\\n<li><strong>Transparency Risk</strong>: Chatbots and deep fakes must be clearly labeled.</li>\\n<li><strong>Minimal/No Risk</strong>: Think video games and spam filters—no extra rules here.</li>\\n</ul>\\n</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Key Dates and Impacts</h2>\\n<p>Starting February 2, 2025, prohibitions and AI literacy rules kick in. By August 2, 2025, general-purpose AI models must meet transparency and copyright standards. The <a href=\\"https://digital-strategy.ec.europa.eu/en/policies/ai-office\\">AI Office</a> is rolling out a Code of Practice to help with compliance, and the <a href=\\"https://digital-strategy.ec.europa.eu/en/policies/ai-pact\\">AI Pact</a> encourages early adoption.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">What It Means for AI Development</h2>\\n<p>This isn’t just red tape—it’s about building trust. Companies need to rethink their AI strategies to prioritize transparency and ethics. It’s a challenge, but it’s also a chance to create AI that truly benefits society.</p>\\n\\n<p>The AI Act is setting the tone for 2025, pushing for responsible innovation. Whether you’re a developer or a business leader, it’s time to get on board with these changes!</p>","image":"/images/blog/4.jpg","category":"ARTICLE","date":"May 24, 2025","slug":"ai-regulatory-landscape-2025","readTime":"5 min read"},{"id":"5","title":"Specialized AI Models: The Future of AI Development?","excerpt":"Explore the debate between specialized and general AI models in 2025 and why hybrids might be the way forward.","content":"<h1>Specialized AI Models: The Future of AI Development?</h1>\\n<p>What’s up, AI fans? In 2025, there’s a big debate brewing: should you go for specialized AI models tailored to specific tasks or stick with general models that do a bit of everything? Let’s break down the pros, cons, and what’s trending.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Specialized AI Models</h2>\\n<p><strong>What Are They?</strong> These are AI systems built for specific jobs, like spotting fraud in finance or analyzing medical images.\\n<ul class=\\"list-disc pl-6 my-4 space-y-2\\">\\n<li><strong>Pros</strong>: Super accurate, less resource-hungry, and great for meeting industry regulations.</li>\\n<li><strong>Cons</strong>: They don’t scale easily across different tasks, and managing multiple models can get messy.</li>\\n<li><strong>Trend</strong>: They’re gaining traction in fields like finance and healthcare, where precision is king, as noted by <a href=\\"https://research.aimultiple.com/specialized-ai/\\">AIMultiple</a>.</li>\\n</ul>\\n</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">General AI Models</h2>\\n<p><strong>What Are They?</strong> Think big systems like GPT-4, handling everything from text to images across industries.\\n<ul class=\\"list-disc pl-6 my-4 space-y-2\\">\\n<li><strong>Pros</strong>: Versatile, easy to deploy, and offer a consistent user experience.</li>\\n<li><strong>Cons</strong>: They need tons of computing power and can miss the mark on niche tasks.</li>\\n<li><strong>Trend</strong>: Still popular for broad applications, but they’re facing competition.</li>\\n</ul>\\n</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Hybrid AI Models</h2>\\n<p><strong>What Are They?</strong> These combine the best of both worlds, using general models with task-specific tweaks.\\n<ul class=\\"list-disc pl-6 my-4 space-y-2\\">\\n<li><strong>Pros</strong>: Flexible, cost-effective, and quick to market.</li>\\n<li><strong>Cons</strong>: Need careful governance to integrate smoothly.</li>\\n<li><strong>Trend</strong>: Hybrids are emerging as the future, offering a balance of power and precision, as discussed by <a href=\\"https://www.ve3.global/specialized-vs-general-ai-models-deciding-the-future-of-ai-architecture-2/\\">VE3</a>.</li>\\n</ul>\\n</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">What’s the Right Choice?</h2>\\n<p>It depends on your needs—your operations, resources, and regulations. Specialized models shine in targeted tasks, general models are great for flexibility, and hybrids might just be the sweet spot. In 2025, it’s all about picking the right tool for the job!</p>","image":"/images/blog/5.jpg","category":"ARTICLE","date":"May 22, 2025","slug":"specialized-ai-models-2025","readTime":"5 min read"},{"id":"6","title":"Ensuring Ethical AI: Key Considerations for 2025","excerpt":"Learn about the top ethical considerations for AI development in 2025, from bias and privacy to job displacement and autonomous weapons.","content":"<h1>Ensuring Ethical AI: Key Considerations for 2025</h1>\\n<p>Hello, AI enthusiasts! As AI gets smarter in 2025, the ethical questions are getting louder. How do we make sure AI does more good than harm? Let’s dive into the top ethical considerations shaping AI development this year.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Bias and Discrimination</h2>\\n<p>AI trained on biased data can amplify unfair outcomes in hiring, lending, and more. U.S. agencies are sounding alarms, pushing for fairer systems, as noted by <a href=\\"https://www.cnbc.com/2023/04/25/us-regulators-warn-they-already-have-the-power-to-go-after-ai-bias.html\\">CNBC</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Transparency and Accountability</h2>\\n<p>AI’s “black box” nature is a problem, especially in healthcare and autonomous vehicles. Efforts to create explainable AI are underway to boost fairness and accuracy, per <a href=\\"https://towardsdatascience.com/what-is-explainable-ai-xai-afc56938d513\\">Towards Data Science</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Creativity and Ownership</h2>\\n<p>Who owns AI-generated art? The rules are murky, and regulations can’t keep up, creating challenges for commercialization, as discussed by <a href=\\"https://www.makeuseof.com/copyright-rules-ai-art/\\">MakeUseOf</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Social Manipulation and Misinformation</h2>\\n<p>Deepfakes and AI-driven misinformation can sway opinions and disrupt elections, a growing concern highlighted by <a href=\\"https://www.vanityfair.com/news/2023/03/ai-2024-deepfake\\">Vanity Fair</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Privacy, Security, and Surveillance</h2>\\n<p>AI’s hunger for data raises privacy issues. China’s facial recognition use, for example, has led to discrimination, as reported by <a href=\\"https://www.npr.org/2023/03/02/1160714485/when-it-comes-to-the-dangers-of-ai-surveillance-poses-more-risk-than-anything\\">NPR</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Job Displacement</h2>\\n<p>AI automation could displace jobs, but it’s also creating new roles. Retraining programs are key to easing this transition, per <a href=\\"https://www.axios.com/2023/03/29/robots-jobs-chatgpt-generative-ai\\">Axios</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Autonomous Weapons</h2>\\n<p>AI-powered weapons spark fears of misuse. International agreements are needed to prevent disasters, as warned by <a href=\\"https://www.theatlantic.com/magazine/archive/2023/06/ai-warfare-nuclear-weapons-strike/673780/\\">The Atlantic</a>.</p>\\n\\n<h2 class=\\"text-2xl font-semibold mt-8 mb-4\\">Policy and Collaboration</h2>\\n<p>The White House is investing $140 million in ethical AI, pushing for collaboration among technologists and policymakers, as noted by <a href=\\"https://www.theverge.com/2023/5/4/23710533/google-microsoft-openai-white-house-ethical-ai-artificial-intelligence\\">The Verge</a>.</p>\\n\\n<p>Ethical AI in 2025 isn’t just a buzzword—it’s a must. By tackling these challenges, we can ensure AI benefits everyone without leaving anyone behind.</p>","image":"/images/blog/6.jpg","category":"ARTICLE","date":"May 20, 2025","slug":"ethical-ai-considerations-2025","readTime":"5 min read"}]');

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTdGFydHVwJTVDJTVDV2Vic2l0ZSU1QyU1Q3N0YXJ0dXBfd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU3RhcnR1cCU1QyU1Q1dlYnNpdGUlNUMlNUNzdGFydHVwX3dlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQXdLO0FBQ3hLO0FBQ0Esc05BQWdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcU3RhcnR1cFxcXFxXZWJzaXRlXFxcXHN0YXJ0dXBfd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFN0YXJ0dXBcXFxcV2Vic2l0ZVxcXFxzdGFydHVwX3dlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcaW1hZ2UtY29tcG9uZW50LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-manrope%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22DM_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-dm-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22dmSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-manrope%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22DM_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-dm-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22dmSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer/Footer.tsx */ \"(ssr)/./src/components/Footer/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar/OuterNav.tsx */ \"(ssr)/./src/components/Navbar/OuterNav.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-manrope%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22DM_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-dm-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22dmSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CStartup%5C%5CWebsite%5C%5Cstartup_website%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar%5C%5COuterNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Footer/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubscribe = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setError(\"\");\n        setMessage(\"\");\n        try {\n            const response = await fetch(\"/api/subscribe\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Something went wrong\");\n            }\n            setMessage(data.message);\n            setEmail(\"\");\n        } catch (err) {\n            // Use type assertion with a custom error interface\n            const errorWithMessage = err;\n            setError(errorWithMessage.message);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative overflow-hidden bg-black pb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container relative mx-auto px-6 md:px-8 lg:px-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-16 sm:grid-cols-2 md:grid-cols-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"inline-block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/images/logo.png\",\n                                        alt: \"Company Logo\",\n                                        width: 140,\n                                        height: 48,\n                                        className: \"h-12 w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-base leading-relaxed text-white/70\",\n                                    children: \"Building intelligent solutions that transform how businesses operate through advanced AI and cutting-edge technology.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 flex space-x-4\",\n                                    children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            \"aria-label\": social.name,\n                                            className: \"flex h-10 w-10 items-center justify-center rounded-full border border-white/10 bg-white/5 text-white/70\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: social.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, social.name, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:grid md:col-span-5 md:grid-cols-3 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-5 text-lg font-semibold text-white\",\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: productLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-sm text-white/60 hover:text-white\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-5 text-lg font-semibold text-white\",\n                                            children: \"Resources\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: resourceLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-sm text-white/60 hover:text-white\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-5 text-lg font-semibold text-white\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: companyLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-sm text-white/60 hover:text-white\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-5 text-lg font-semibold text-white\",\n                                    children: \"Stay Connected\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-5 text-base text-white/70\",\n                                    children: \"Subscribe to receive updates on new features and announcements.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"relative\",\n                                    onSubmit: handleSubscribe,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-hidden rounded-lg bg-white/5 p-0.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    name: \"email\",\n                                                    placeholder: \"Enter your email\",\n                                                    className: \"w-full bg-black/30 py-3 px-4 text-white placeholder:text-white/40 focus:outline-none\",\n                                                    required: true,\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px w-full bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"mt-4 flex w-full items-center justify-center rounded-md bg-white py-3 px-4 text-sm font-medium text-black transition-all duration-300 cursor-pointer hover:bg-white/[0.85] hover:shadow-md hover:translate-y-[-4px]\",\n                                                    disabled: isSubmitting,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSubmitting ? \"Subscribing...\" : \"Subscribe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        !isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-teal-400\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-red-400\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 pt-8 border-t border-white/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-white/50\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Your Company. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-sm text-white/50 hover:text-white/80\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-sm text-white/50 hover:text-white/80\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-sm text-white/50 hover:text-white/80\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n// Social media icons\nconst socialLinks = [\n    {\n        name: \"Twitter\",\n        href: \"https://twitter.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"LinkedIn\",\n        href: \"https://linkedin.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6.5 21.5h-5v-13h5v13zM4 6.5C2.5 6.5 1.5 5.3 1.5 4s1-2.4 2.5-2.4c1.6 0 2.5 1 2.6 2.5 0 1.4-1 2.5-2.6 2.5zm11.5 6c-1 0-2 1-2 2v7h-5v-13h5v1.8c.8-1 2.1-1.8 3.5-1.8 2.5 0 5 2.1 5 6.1V21.5h-5V14.5c0-1-1-2-2-2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"GitHub\",\n        href: \"https://github.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Instagram\",\n        href: \"https://instagram.com\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"h-4 w-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                clipRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, undefined)\n    }\n];\n// Footer link sections\nconst productLinks = [\n    {\n        name: \"AI Automation\",\n        href: \"/products/ai-automation\"\n    },\n    {\n        name: \"Workflow Tools\",\n        href: \"/products/workflow\"\n    },\n    {\n        name: \"Data Analytics\",\n        href: \"/products/analytics\"\n    },\n    {\n        name: \"Custom Solutions\",\n        href: \"/products/custom\"\n    }\n];\nconst resourceLinks = [\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Documentation\",\n        href: \"/docs\"\n    },\n    {\n        name: \"Case Studies\",\n        href: \"/case-studies\"\n    },\n    {\n        name: \"Help Center\",\n        href: \"/help\"\n    }\n];\nconst companyLinks = [\n    {\n        name: \"About Us\",\n        href: \"/about\"\n    },\n    {\n        name: \"Careers\",\n        href: \"/careers\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    },\n    {\n        name: \"Partners\",\n        href: \"/partners\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar/InnerNav.tsx":
/*!********************************************!*\
  !*** ./src/components/Navbar/InnerNav.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InnerNav: () => (/* binding */ InnerNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/navbar-menu */ \"(ssr)/./src/components/ui/navbar-menu.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ InnerNav auto */ \n\n\n\n\n\nfunction InnerNav({ className }) {\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-x-0 z-[100] w-fit mx-auto py-2\", className),\n        initial: {\n            opacity: 0.9\n        },\n        animate: {\n            opacity: 1\n        },\n        whileHover: {\n            opacity: 1\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.Menu, {\n                setActive: setActive,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/agents\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            setActive: setActive,\n                            active: active,\n                            item: \"AI Agents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/resources\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            setActive: setActive,\n                            active: active,\n                            item: \"Resources\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/about\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_navbar_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            setActive: setActive,\n                            active: active,\n                            item: \"About\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\InnerNav.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OYXZiYXIvSW5uZXJOYXYudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUN3QztBQUNXO0FBQ2xCO0FBQ3NCO0FBQzFCO0FBRXRCLFNBQVNRLFNBQVMsRUFBRUMsU0FBUyxFQUEwQjtJQUM1RCxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR1YsK0NBQVFBLENBQWdCO0lBQ3BELHFCQUNFLDhEQUFDSyxnREFBTUEsQ0FBQ00sR0FBRztRQUNUSCxXQUFXTCw4Q0FBRUEsQ0FBQyw4Q0FBOENLO1FBQzVESSxTQUFTO1lBQUVDLFNBQVM7UUFBSTtRQUN4QkMsU0FBUztZQUFFRCxTQUFTO1FBQUU7UUFDdEJFLFlBQVk7WUFBRUYsU0FBUztRQUFFO2tCQUV6Qiw0RUFBQ1QseURBQWVBO3NCQUNkLDRFQUFDSCxpREFBSUE7Z0JBQUNTLFdBQVdBOztrQ0FDZiw4REFBQ0osa0RBQUlBO3dCQUFDVSxNQUFNO2tDQUNWLDRFQUFDZCxxREFBUUE7NEJBQUNRLFdBQVdBOzRCQUFXRCxRQUFRQTs0QkFBUVEsTUFBSzs7Ozs7Ozs7Ozs7a0NBRXZELDhEQUFDWCxrREFBSUE7d0JBQUNVLE1BQU07a0NBQ1YsNEVBQUNkLHFEQUFRQTs0QkFBQ1EsV0FBV0E7NEJBQVdELFFBQVFBOzRCQUFRUSxNQUFLOzs7Ozs7Ozs7OztrQ0FFdkQsOERBQUNYLGtEQUFJQTt3QkFBQ1UsTUFBTTtrQ0FDViw0RUFBQ2QscURBQVFBOzRCQUFDUSxXQUFXQTs0QkFBV0QsUUFBUUE7NEJBQVFRLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1qRSIsInNvdXJjZXMiOlsiRDpcXFN0YXJ0dXBcXFdlYnNpdGVcXHN0YXJ0dXBfd2Vic2l0ZVxcc3JjXFxjb21wb25lbnRzXFxOYXZiYXJcXElubmVyTmF2LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IE1lbnUsIE1lbnVJdGVtIH0gZnJvbSBcIi4uL3VpL25hdmJhci1tZW51XCI7XHJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XHJcbmltcG9ydCB7IEFuaW1hdGVQcmVzZW5jZSwgbW90aW9uIH0gZnJvbSBcIm1vdGlvbi9yZWFjdFwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gSW5uZXJOYXYoeyBjbGFzc05hbWUgfTogeyBjbGFzc05hbWU/OiBzdHJpbmcgfSkge1xyXG4gIGNvbnN0IFthY3RpdmUsIHNldEFjdGl2ZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICByZXR1cm4gKFxyXG4gICAgPG1vdGlvbi5kaXZcclxuICAgICAgY2xhc3NOYW1lPXtjbihcImZpeGVkIGluc2V0LXgtMCB6LVsxMDBdIHctZml0IG14LWF1dG8gcHktMlwiLCBjbGFzc05hbWUpfVxyXG4gICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAuOSB9fVxyXG4gICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cclxuICAgICAgd2hpbGVIb3Zlcj17eyBvcGFjaXR5OiAxIH19XHJcbiAgICA+XHJcbiAgICAgIDxBbmltYXRlUHJlc2VuY2U+XHJcbiAgICAgICAgPE1lbnUgc2V0QWN0aXZlPXtzZXRBY3RpdmV9PlxyXG4gICAgICAgICAgPExpbmsgaHJlZj17XCIvYWdlbnRzXCJ9PlxyXG4gICAgICAgICAgICA8TWVudUl0ZW0gc2V0QWN0aXZlPXtzZXRBY3RpdmV9IGFjdGl2ZT17YWN0aXZlfSBpdGVtPVwiQUkgQWdlbnRzXCIgLz5cclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgIDxMaW5rIGhyZWY9e1wiL3Jlc291cmNlc1wifT5cclxuICAgICAgICAgICAgPE1lbnVJdGVtIHNldEFjdGl2ZT17c2V0QWN0aXZlfSBhY3RpdmU9e2FjdGl2ZX0gaXRlbT1cIlJlc291cmNlc1wiIC8+XHJcbiAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICA8TGluayBocmVmPXtcIi9hYm91dFwifT5cclxuICAgICAgICAgICAgPE1lbnVJdGVtIHNldEFjdGl2ZT17c2V0QWN0aXZlfSBhY3RpdmU9e2FjdGl2ZX0gaXRlbT1cIkFib3V0XCIgLz5cclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICA8L01lbnU+XHJcbiAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxyXG4gICAgPC9tb3Rpb24uZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJNZW51IiwiTWVudUl0ZW0iLCJjbiIsIkFuaW1hdGVQcmVzZW5jZSIsIm1vdGlvbiIsIkxpbmsiLCJJbm5lck5hdiIsImNsYXNzTmFtZSIsImFjdGl2ZSIsInNldEFjdGl2ZSIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwiYW5pbWF0ZSIsIndoaWxlSG92ZXIiLCJocmVmIiwiaXRlbSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar/InnerNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar/OuterNav.tsx":
/*!********************************************!*\
  !*** ./src/components/Navbar/OuterNav.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _InnerNav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InnerNav */ \"(ssr)/./src/components/Navbar/InnerNav.tsx\");\n/* harmony import */ var _ui_shimmer_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/shimmer-button */ \"(ssr)/./src/components/ui/shimmer-button.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst OuterNav = ()=>{\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Add scroll event listener to change navbar appearance on scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"OuterNav.useEffect\": ()=>{\n            const handleScroll = {\n                \"OuterNav.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 10);\n                }\n            }[\"OuterNav.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"OuterNav.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"OuterNav.useEffect\"];\n        }\n    }[\"OuterNav.useEffect\"], []);\n    // Toggle mobile menu\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 w-full z-50 transition-all duration-300 ${scrolled ? \"backdrop-blur-lg bg-transparent shadow-sm\" : \"backdrop-blur-sm bg-transparent\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-32\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 md:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/images/logo.png\" // Replace with your logo path\n                                ,\n                                alt: \"Company Logo\",\n                                width: 120,\n                                height: 40,\n                                className: \"h-16 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InnerNav__WEBPACK_IMPORTED_MODULE_4__.InnerNav, {\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_shimmer_button__WEBPACK_IMPORTED_MODULE_5__.ShimmerButton, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Book Free Consulting\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-white hover:text-blue-200 focus:outline-none relative w-6 h-6\",\n                                onClick: toggleMobileMenu,\n                                \"aria-label\": \"Toggle menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            className: \"absolute top-2 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu\",\n                                            animate: mobileMenuOpen ? {\n                                                rotate: 45,\n                                                y: 6\n                                            } : {\n                                                rotate: 0,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            className: \"absolute top-3.5 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu\",\n                                            animate: mobileMenuOpen ? {\n                                                opacity: 0\n                                            } : {\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            className: \"absolute top-5 left-0 block w-6 h-0.5 bg-current rounded-sm transform-gpu\",\n                                            animate: mobileMenuOpen ? {\n                                                rotate: -45,\n                                                y: -6\n                                            } : {\n                                                rotate: 0,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"md:hidden fixed left-0 right-0 overflow-hidden\",\n                        initial: {\n                            height: 0,\n                            opacity: 0\n                        },\n                        animate: {\n                            height: \"auto\",\n                            opacity: 1\n                        },\n                        exit: {\n                            height: 0,\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/90 backdrop-blur-lg shadow-lg py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/agents\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"AI Agents\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/resources\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Resources\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/about\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/contact\",\n                                        className: \"w-full text-center py-2 text-white hover:text-blue-300 text-lg font-medium transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\Navbar\\\\OuterNav.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OuterNav);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar/OuterNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/navbar-menu.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/navbar-menu.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoveredLink: () => (/* binding */ HoveredLink),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   ProductItem: () => (/* binding */ ProductItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ MenuItem,Menu,ProductItem,HoveredLink auto */ \n\n\n\nconst transition = {\n    type: \"spring\",\n    mass: 0.5,\n    damping: 11.5,\n    stiffness: 100,\n    restDelta: 0.001,\n    restSpeed: 0.001\n};\nconst MenuItem = ({ setActive, active, item, children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseEnter: ()=>setActive(item),\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                transition: {\n                    duration: 0.3\n                },\n                className: \"cursor-pointer text-white hover:opacity-[0.9] py-2 px-6 rounded-full hover:bg-white/10 transition-colors\",\n                children: item\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            active !== null && children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.85,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.85,\n                    y: 10\n                },\n                transition: transition,\n                children: active === item && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-[calc(100%_+_1rem)] left-1/2 transform -translate-x-1/2 pt-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            scale: 0.8,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.8,\n                            opacity: 0\n                        },\n                        transition: transition,\n                        layoutId: \"active\",\n                        className: \"bg-black text-white backdrop-blur-sm rounded-2xl overflow-hidden border border-white/[0.2] shadow-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            layout: true,\n                            className: \"w-max h-full p-4\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\nconst Menu = ({ setActive, children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_3__.motion.nav, {\n        initial: {\n            backgroundColor: \"rgba(0,0,0,0)\"\n        },\n        whileHover: {\n            backgroundColor: \"rgba(0,0,0,1)\",\n            transition: {\n                duration: 0.2\n            }\n        },\n        onMouseLeave: ()=>setActive(null),\n        className: \"relative rounded-full border border-white/[0.015] bg-transparent text-white text-lg font-semibold flex justify-center px-2.5 py-2 w-fit transition-colors duration-200\",\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\nconst ProductItem = ({ title, description, href, src })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"flex space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: src,\n                alt: title,\n                width: 400,\n                height: 200,\n                className: \"h-full w-full object-cover transition-all duration-500 group-hover:scale-105\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-xl font-bold mb-1 text-black dark:text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-700 text-sm max-w-[10rem] dark:text-neutral-300\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\nconst HoveredLink = ({ children, ...rest })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        ...rest,\n        className: \"text-white hover:text-gray-300\",\n        onClick: (e)=>{\n            e.stopPropagation();\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\navbar-menu.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/navbar-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/shimmer-button.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/shimmer-button.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShimmerButton: () => (/* binding */ ShimmerButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst ShimmerButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ shimmerColor = \"#ffffff\", shimmerSize = \"0.1em\", shimmerDuration = \"2.5s\", borderRadius = \"18px\", background = \"black\", hoverShimmerColor = \"#ffffff\", className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: {\n            \"--spread\": \"120deg\",\n            \"--shimmer-color\": shimmerColor,\n            \"--hover-shimmer-color\": hoverShimmerColor,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": background\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 px-4 py-2.5 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black font-semibold\", \"transform-gpu transition-all duration-300 ease-in-out active:translate-y-px hover:translate-y-[-3px]\", className),\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-z-30 blur-[2px]\", \"absolute inset-0 overflow-visible [container-type:size]\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0] group-hover:[background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--hover-shimmer-color)_var(--spread),transparent_var(--spread))]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"insert-0 absolute size-full\", \"rounded-2xl px-4 py-1.5 text-sm font-medium\", // transition\n                \"transform-gpu transition-all duration-300 ease-in-out\", \"group-hover:scale-[1.03]\" // Scale effect on hover\n                )\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)] transition-all duration-300\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Startup\\\\Website\\\\startup_website\\\\src\\\\components\\\\ui\\\\shimmer-button.tsx\",\n        lineNumber: 35,\n        columnNumber: 7\n    }, undefined);\n});\nShimmerButton.displayName = \"ShimmerButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/shimmer-button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXFN0YXJ0dXBcXFdlYnNpdGVcXHN0YXJ0dXBfd2Vic2l0ZVxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcclxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcclxuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXHJcbn1cclxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/motion","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/tailwind-merge","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=D%3A%5CStartup%5CWebsite%5Cstartup_website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStartup%5CWebsite%5Cstartup_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();