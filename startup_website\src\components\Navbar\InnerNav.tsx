"use client";
import React, { useState } from "react";
import { Menu, MenuItem } from "../ui/navbar-menu";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "motion/react";
import Link from "next/link";

export function InnerNav({ className }: { className?: string }) {
  const [active, setActive] = useState<string | null>(null);
  return (
    <motion.div
      className={cn("fixed inset-x-0 z-[100] w-fit mx-auto py-2", className)}
      initial={{ opacity: 0.9 }}
      animate={{ opacity: 1 }}
      whileHover={{ opacity: 1 }}
    >
      <AnimatePresence>
        <Menu setActive={setActive}>
          <Link href={"/agents"}>
            <MenuItem setActive={setActive} active={active} item="AI Agents" />
          </Link>
          <Link href={"/resources"}>
            <MenuItem setActive={setActive} active={active} item="Resources" />
          </Link>
          <Link href={"/about"}>
            <MenuItem setActive={setActive} active={active} item="About" />
          </Link>
        </Menu>
      </AnimatePresence>
    </motion.div>
  );
}
